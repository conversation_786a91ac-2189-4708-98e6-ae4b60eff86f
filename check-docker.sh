#!/bin/bash

# AakaarAI Docker Status Check Script
echo "🐳 Checking AakaarAI Docker Setup..."
echo "===================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    echo "   Install from: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running"
    echo "   Please start Docker first"
    exit 1
fi

echo "✅ Docker is installed and running"

# Check MySQL container
echo ""
echo "🗄️  MySQL Container Status:"
if docker ps | grep -q mysql-aakaar; then
    echo "✅ MySQL container is running"
    MYSQL_STATUS=$(docker ps --filter "name=mysql-aakaar" --format "table {{.Status}}")
    echo "   Status: $MYSQL_STATUS"
    
    # Test MySQL connection
    if docker exec mysql-aakaar mysqladmin ping -h localhost --silent 2>/dev/null; then
        echo "✅ MySQL is responding"
        
        # Check if database exists
        if docker exec mysql-aakaar mysql -u root -proot -e "USE AakaarAI; SELECT 1;" &>/dev/null; then
            echo "✅ AakaarAI database exists"
        else
            echo "⚠️  AakaarAI database not found"
            echo "   Creating database..."
            docker exec mysql-aakaar mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS AakaarAI;"
            echo "✅ Database created"
        fi
    else
        echo "❌ MySQL is not responding"
    fi
elif docker ps -a | grep -q mysql-aakaar; then
    echo "⚠️  MySQL container exists but is not running"
    echo "   Starting container..."
    docker start mysql-aakaar
    echo "✅ MySQL container started"
else
    echo "❌ MySQL container not found"
    echo "   Run: ./docker-setup.sh to create it"
fi

# Check Redis container
echo ""
echo "🔴 Redis Container Status:"
if docker ps | grep -q redis-aakaar; then
    echo "✅ Redis container is running"
    REDIS_STATUS=$(docker ps --filter "name=redis-aakaar" --format "table {{.Status}}")
    echo "   Status: $REDIS_STATUS"
    
    # Test Redis connection
    if docker exec redis-aakaar redis-cli ping | grep -q PONG; then
        echo "✅ Redis is responding"
    else
        echo "❌ Redis is not responding"
    fi
elif docker ps -a | grep -q redis-aakaar; then
    echo "⚠️  Redis container exists but is not running"
    echo "   Starting container..."
    docker start redis-aakaar
    echo "✅ Redis container started"
else
    echo "❌ Redis container not found"
    echo "   Run: ./docker-setup.sh to create it"
fi

# Check ports
echo ""
echo "🔌 Port Status:"
if netstat -tuln 2>/dev/null | grep -q :3306; then
    echo "✅ Port 3306 (MySQL) is in use"
else
    echo "⚠️  Port 3306 (MySQL) is not in use"
fi

if netstat -tuln 2>/dev/null | grep -q :6379; then
    echo "✅ Port 6379 (Redis) is in use"
else
    echo "⚠️  Port 6379 (Redis) is not in use"
fi

# Show container details
echo ""
echo "📊 Container Details:"
echo "===================="
docker ps --filter "name=mysql-aakaar" --filter "name=redis-aakaar" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"

# Check Docker Compose
echo ""
echo "🐙 Docker Compose Status:"
if command -v docker-compose &> /dev/null; then
    echo "✅ Docker Compose is available"
    if [ -f docker-compose.yml ]; then
        echo "✅ docker-compose.yml found"
        echo "   You can use: docker-compose up -d"
    else
        echo "⚠️  docker-compose.yml not found"
    fi
else
    echo "⚠️  Docker Compose not installed"
fi

# Environment check
echo ""
echo "📝 Environment Configuration:"
if [ -f .env ]; then
    echo "✅ .env file exists"
    echo "   Database settings:"
    grep "^DB_" .env | head -5
    echo "   Redis settings:"
    grep "^REDIS_" .env | head -2
else
    echo "⚠️  .env file not found"
    echo "   Run: cp .env.example .env"
fi

# Final recommendations
echo ""
echo "💡 Recommendations:"
echo "==================="

ISSUES=0

if ! docker ps | grep -q mysql-aakaar; then
    echo "🔧 Start MySQL: docker start mysql-aakaar"
    ISSUES=$((ISSUES + 1))
fi

if ! docker ps | grep -q redis-aakaar; then
    echo "🔧 Start Redis: docker start redis-aakaar"
    ISSUES=$((ISSUES + 1))
fi

if [ ! -f .env ]; then
    echo "🔧 Create .env: cp .env.example .env"
    ISSUES=$((ISSUES + 1))
fi

if [ $ISSUES -eq 0 ]; then
    echo "🎉 Everything looks good! You can start the application with:"
    echo "   ./start.sh"
else
    echo "⚠️  Please resolve the issues above, then run:"
    echo "   ./start.sh"
fi

echo ""
echo "🚀 Happy analyzing!"
