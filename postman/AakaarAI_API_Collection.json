{"info": {"name": "AakaarAI API Collection", "description": "Complete API collection for AakaarAI PDF Analysis Platform", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}], "item": [{"name": "Health Check", "item": [{"name": "System Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check system health and status"}}]}, {"name": "Upload API", "item": [{"name": "Upload CSV File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/file.csv", "description": "CSV file to upload for analysis"}]}, "url": {"raw": "{{baseUrl}}/upload", "host": ["{{baseUrl}}"], "path": ["upload"]}, "description": "Upload a CSV file for analysis. Returns job ID for tracking."}}, {"name": "Get Upload Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/upload/{{jobId}}", "host": ["{{baseUrl}}"], "path": ["upload", "{{jobId}}"]}, "description": "Get the status of an upload job"}}, {"name": "Get Recent Uploads", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/upload?limit=10&offset=0", "host": ["{{baseUrl}}"], "path": ["upload"], "query": [{"key": "limit", "value": "10", "description": "Number of uploads to return"}, {"key": "offset", "value": "0", "description": "Offset for pagination"}]}, "description": "Get list of recent uploads with pagination"}}, {"name": "Get Upload Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/upload/stats/summary", "host": ["{{baseUrl}}"], "path": ["upload", "stats", "summary"]}, "description": "Get overall upload statistics and metrics"}}, {"name": "Retry Failed Job", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/upload/{{jobId}}/retry", "host": ["{{baseUrl}}"], "path": ["upload", "{{jobId}}", "retry"]}, "description": "<PERSON>try a failed analysis job"}}, {"name": "Delete Upload Job", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/upload/{{jobId}}", "host": ["{{baseUrl}}"], "path": ["upload", "{{jobId}}"]}, "description": "Delete an upload job and associated data"}}]}, {"name": "Analysis API", "item": [{"name": "Get Analysis Results", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/analysis/{{jobId}}?includeRawData=false", "host": ["{{baseUrl}}"], "path": ["analysis", "{{jobId}}"], "query": [{"key": "includeRawData", "value": "false", "description": "Include raw processed data in response"}]}, "description": "Get complete analysis results for a job"}}, {"name": "Get Analysis Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/analysis/{{jobId}}/summary", "host": ["{{baseUrl}}"], "path": ["analysis", "{{jobId}}", "summary"]}, "description": "Get lightweight analysis summary"}}, {"name": "Get Analysis Insights", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/analysis/{{jobId}}/insights", "host": ["{{baseUrl}}"], "path": ["analysis", "{{jobId}}", "insights"]}, "description": "Get AI-generated insights only"}}, {"name": "Export Analysis Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/analysis/{{jobId}}/export?format=json", "host": ["{{baseUrl}}"], "path": ["analysis", "{{jobId}}", "export"], "query": [{"key": "format", "value": "json", "description": "Export format: json or csv"}]}, "description": "Export analysis data in specified format"}}, {"name": "Compare Multiple Analyses", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"jobIds\": [\"job-id-1\", \"job-id-2\", \"job-id-3\"]\n}"}, "url": {"raw": "{{baseUrl}}/analysis/compare", "host": ["{{baseUrl}}"], "path": ["analysis", "compare"]}, "description": "Compare metrics across multiple analysis jobs"}}]}, {"name": "Optimization API", "item": [{"name": "Generate Optimization Tasks", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/optimize/{{jobId}}", "host": ["{{baseUrl}}"], "path": ["optimize", "{{jobId}}"]}, "description": "Generate AI-powered optimization tasks for an analysis"}}, {"name": "Get Optimization Tasks", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/optimize/{{jobId}}?status=pending&priority=high", "host": ["{{baseUrl}}"], "path": ["optimize", "{{jobId}}"], "query": [{"key": "status", "value": "pending", "description": "Filter by task status"}, {"key": "priority", "value": "high", "description": "Filter by task priority"}, {"key": "category", "value": "", "description": "Filter by task category", "disabled": true}]}, "description": "Get optimization tasks with optional filters"}}, {"name": "Get Optimization Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/optimize/{{jobId}}/summary", "host": ["{{baseUrl}}"], "path": ["optimize", "{{jobId}}", "summary"]}, "description": "Get optimization summary and statistics"}}, {"name": "Get Specific Task", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/optimize/task/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["optimize", "task", "{{taskId}}"]}, "description": "Get details of a specific optimization task"}}, {"name": "Update Task Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\",\n  \"notes\": \"Task completed successfully\"\n}"}, "url": {"raw": "{{baseUrl}}/optimize/task/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["optimize", "task", "{{taskId}}"]}, "description": "Update optimization task status and notes"}}, {"name": "Delete Optimization Task", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/optimize/task/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["optimize", "task", "{{taskId}}"]}, "description": "Delete an optimization task"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set common variables", "if (!pm.environment.get('jobId')) {", "    pm.environment.set('jobId', 'your-job-id-here');", "}", "", "if (!pm.environment.get('taskId')) {", "    pm.environment.set('taskId', 'your-task-id-here');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test scripts", "pm.test('Status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 202]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "// Auto-extract jobId from upload response", "if (pm.request.url.path.includes('upload') && pm.response.code === 201) {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.jobId) {", "        pm.environment.set('jobId', jsonData.data.jobId);", "        console.log('Job ID set to:', jsonData.data.jobId);", "    }", "}"]}}]}