# AakaarAI - Advanced PDF Analysis Platform

A sophisticated web application for PDF reading, CSV data analysis, and AI-powered insights generation. Built with Node.js backend, React.js frontend, MySQL database, and Redis for caching and job queues.

## 🚀 Features

### Core Functionality
- **PDF Reading & CSV Processing**: Upload and process CSV files with comprehensive data validation
- **Real-time Analysis**: Background job processing with live progress tracking
- **AI-Powered Insights**: LangChain agents for data analysis and optimization recommendations
- **Performance Optimization**: Redis caching, compression, and efficient data processing

### Technical Features
- **Robust Backend**: Node.js with Express.js, Sequelize ORM, and MySQL
- **Modern Frontend**: React.js with responsive design and real-time updates
- **Job Queue System**: BullMQ for background processing and task management
- **Security**: Helmet, CORS, rate limiting, and input validation
- **Monitoring**: Winston logging, Morgan middleware, and comprehensive error handling

## 🛠 Technology Stack

### Backend
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: MySQL with Sequelize ORM
- **Cache/Queue**: Redis with BullMQ
- **AI/ML**: LangChain with OpenAI integration
- **Security**: Helm<PERSON>, CORS, express-rate-limit
- **Logging**: Winston, Morgan
- **File Processing**: Multer, csv-parser

### Frontend
- **Framework**: React.js 18+
- **Routing**: React Router DOM
- **Styling**: Tailwind CSS
- **UI Components**: Lucide React icons
- **Charts**: Recharts
- **File Upload**: React Dropzone
- **Notifications**: React Hot Toast

### Infrastructure
- **Containerization**: Docker (MySQL, Redis)
- **Process Management**: PM2 (optional)
- **API Documentation**: Postman collection included

## 📋 Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose
- MySQL 8.0+ (via Docker)
- Redis 6+ (via Docker)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd AakaarAI
```

### 2. Install Dependencies
```bash
# Install backend dependencies
npm install

# Install frontend dependencies
cd frontend
npm install
cd ..
```

### 3. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Edit .env with your configuration
# The default settings work with the provided Docker setup
```

### 4. Start Infrastructure Services
```bash
# Start MySQL and Redis using existing Docker containers
# Make sure your Docker containers are running:
# - MySQL on port 3306
# - Redis on port 6379
```

### 5. Initialize Database
```bash
# The application will automatically create tables on first run
npm start
```

### 6. Start the Application

#### Development Mode
```bash
# Terminal 1: Start backend server
npm run dev

# Terminal 2: Start workers
node workers.js

# Terminal 3: Start frontend
cd frontend
npm start
```

#### Production Mode
```bash
# Build frontend
cd frontend
npm run build
cd ..

# Start backend (serves frontend)
npm start

# Start workers (separate process)
node workers.js
```

## 📁 Project Structure

```
AakaarAI/
├── src/
│   ├── config/          # Database, Redis, logging configuration
│   ├── models/          # Sequelize database models
│   ├── routes/          # Express.js route handlers
│   ├── controllers/     # Business logic controllers
│   ├── services/        # Core business services
│   ├── middleware/      # Custom middleware
│   ├── workers/         # Background job workers
│   ├── agents/          # LangChain AI agents
│   └── utils/           # Utility functions
├── frontend/
│   ├── src/
│   │   ├── components/  # React components
│   │   ├── pages/       # Page components
│   │   ├── services/    # API service layer
│   │   └── utils/       # Frontend utilities
│   └── public/          # Static assets
├── uploads/             # File upload directory
├── logs/                # Application logs
├── postman/             # API documentation
└── workers.js           # Worker process starter
```

## 🔧 Configuration

### Environment Variables
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=AakaarAI
DB_USER=root
DB_PASSWORD=root

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# AI Configuration (Optional)
OPENAI_API_KEY=your_openai_api_key_here

# File Upload
MAX_FILE_SIZE=104857600  # 100MB
UPLOAD_DIR=./uploads
```

## 📊 API Documentation

### Upload API
- `POST /api/upload` - Upload CSV file
- `GET /api/upload/:jobId` - Get upload status
- `GET /api/upload` - List recent uploads
- `GET /api/upload/stats/summary` - Upload statistics

### Analysis API
- `GET /api/analysis/:id` - Get analysis results
- `GET /api/analysis/:id/summary` - Get analysis summary
- `GET /api/analysis/:id/insights` - Get AI insights
- `GET /api/analysis/:id/export` - Export analysis data
- `POST /api/analysis/compare` - Compare multiple analyses

### Optimization API
- `POST /api/optimize/:id` - Generate optimization tasks
- `GET /api/optimize/:id` - Get optimization tasks
- `GET /api/optimize/task/:taskId` - Get specific task
- `PUT /api/optimize/task/:taskId` - Update task status

### Import Postman Collection
Import `postman/AakaarAI_API_Collection.json` into Postman for complete API documentation with examples.

## 🎯 Usage Guide

### 1. Upload CSV File
1. Navigate to `/upload`
2. Drag and drop or select a CSV file
3. Monitor upload and analysis progress
4. View results when processing completes

### 2. View Analysis Results
1. Go to `/results/:jobId` or click "View Results" from dashboard
2. Explore different tabs:
   - **Overview**: Key metrics and summary
   - **Performance**: Detailed performance analysis
   - **Insights**: AI-generated insights and recommendations
   - **Optimization**: Actionable optimization tasks

### 3. Generate Optimizations
1. In the Optimization tab, click "Generate Optimizations"
2. Review AI-generated tasks organized by category
3. Track task completion and priority

## 🔍 Data Processing

### Supported File Formats
- CSV (.csv)
- Excel (.xls, .xlsx)
- Text files (.txt)
- Maximum file size: 100MB

### Expected Data Structure
For optimal results, CSV files should contain columns like:
- Matched Product
- Impressions
- Clicks
- Spend (USD)
- Sales (USD)
- Orders
- CTR
- CPC (USD)
- ACOS
- ROAS
- Conversion Rate

## 🤖 AI Agents

### DataAnalyzer Agent
- Analyzes CSV data for patterns and anomalies
- Performs statistical analysis
- Identifies data quality issues

### InsightGenerator Agent
- Generates strategic insights from analysis
- Provides executive summaries
- Identifies opportunities and risks

### TaskCreator Agent
- Creates actionable optimization tasks
- Prioritizes tasks by impact and effort
- Provides implementation guidance

## 🚀 Performance Optimization

### Backend Optimizations
- Redis caching for analysis results
- Database query optimization with Sequelize
- Compression middleware for responses
- Rate limiting for API protection
- Background job processing with BullMQ

### Frontend Optimizations
- Code splitting and lazy loading
- Optimized bundle size with Webpack
- Responsive design for all devices
- Real-time progress updates
- Efficient state management

## 🔒 Security Features

- **Input Validation**: Joi schema validation
- **File Upload Security**: Type and size restrictions
- **Rate Limiting**: Configurable request limits
- **CORS Protection**: Controlled cross-origin requests
- **Helmet Security**: Security headers
- **Error Handling**: Secure error responses

## 📝 Logging

### Log Levels
- **Error**: Application errors and exceptions
- **Warn**: Warning messages
- **Info**: General information
- **Debug**: Detailed debugging information

### Log Files
- `logs/error.log` - Error logs only
- `logs/combined.log` - All logs
- Console output in development

## 🧪 Testing

```bash
# Run backend tests
npm test

# Run frontend tests
cd frontend
npm test
```

## 🚀 Deployment

### Production Deployment
1. Set `NODE_ENV=production`
2. Build frontend: `cd frontend && npm run build`
3. Start application: `npm start`
4. Start workers: `node workers.js`
5. Configure reverse proxy (nginx)
6. Set up SSL certificates
7. Configure monitoring and logging

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the Postman collection
- Check application logs
- Create an issue in the repository

## 🔄 Version History

### v1.0.0
- Initial release
- PDF reading and CSV processing
- AI-powered analysis and insights
- React.js frontend with real-time updates
- Complete API with Postman documentation
