Aim
Build a backend-focused web application with sophisticated data processing and AI agent
capabilities that allows users to upload ad data, perform complex analysis, and generate
optimisation strategies.

Ad CSV File
Sponsored_Products_adgroup_search_terms_Jan_18_2025.csv

Core Technical Requirements
1. Backend Architecture & API Development
API Design & Implementation
Design and implement a robust RESTful API with the following endpoints:
●​ POST /api/upload
○​ Handle large CSV file uploads (up to 100MB)
○​ Implement file validation and sanitisation
○​ Return job ID for async processing
○​ Store file metadata and processing status
●​ GET /api/analysis/:id
○​ Implement polling mechanism for job status
○​ Return comprehensive analysis results
○​ Handle various response states (processing, completed, failed)
○​ Implement proper caching for completed analyses
●​ POST /api/optimize/:id
○​ Generate optimisation tasks based on analysis
○​ Implement task prioritisation logic
○​ Create actionable recommendations

Data Processing Pipeline
●​ CSV Parser Implementation: Build robust CSV parsing with error handling
●​ Data Validation Layer: Validate data integrity, handle missing values
●​ Metric Calculation Engine:
○​ Calculate ROAS (Return on Ad Spend)
○​ Calculate ACOS (Advertising Cost of Sale)
○​ Calculate CTR (Click-Through Rate)

○​ Implement statistical analysis for performance trends
○​ Identify top/bottom performing keywords

Asynchronous Job Processing
●​
●​
●​
●​

Implement job queue
Design worker processes for background analysis
Handle job failures and retries
Implement job status tracking and updates

Database Design & Implementation
●​ Design normalised schema for:
○​ Analysis jobs and status
○​ Processed metrics and results
○​ Optimisation tasks
○​ Historical data for trend analysis
●​ Implement efficient queries and indexes
●​ Handle data persistence and retrieval

2. LangChain Agent Development (Advanced Backend Feature)
Multi Agent Architecture
Design and implement a LangChain agent system:
Tool Development:
●​ DataAnalyzer Agent:
○​ Process CSV data programmatically
○​ Calculate complex metrics
○​ Identify patterns and anomalies
●​ InsightGenerator Agent:
○​ Integrate with LLM (Claude/OpenAI/Gemini/Open Source)
○​ Generate human-readable insights
○​ Implement prompt engineering for consistent outputs
●​ TaskCreator Agent:
○​ Generate optimisation strategies
○​ Create prioritised action items
○​ Implement decision logic for recommendations

3. Frontend (Minimal Implementation)
Create a simple React interface with three screens:

1.​ Landing Page - Basic dashboard
2.​ Upload Screen - File upload functionality
3.​ Results Screen - Display analysis results
Note: Frontend should be functional but minimal - focus is on backend excellence

* Agent Workflow Implementation (Stretch goal)
●​
●​
●​
●​

Build stateful agent that maintains context
Implement error handling for LLM failures
Create fallback mechanisms
Design agent memory for improved responses

Tech Stack Details
Backend Stack
●​
●​
●​
●​
●​

Runtime: Node.js
Framework: Express.js
Database: Supabase or PostgreSQL or any other
LLM Framework: LangChain or no framework
Containerisation: Docker (optional)

Submission Requirements
1. Code Repository:
●​ Create a GitHub repository for the project
●​ Use clear and informative commit messages
●​ Make the project repository private and add the username Princejain1101 as a
collaborator

2. README.md:
Provide a comprehensive README.md file that includes:
●​ Project Overview: A brief description of the project and its functionality (around 150
words)

●​ Architecture Diagram: A simple diagram illustrating the system's architecture
(interaction between frontend, backend, LangChain agent, and LLM API)
●​ Setup Instructions: Detailed instructions on how to set up and run the project locally,
including installing dependencies
●​ API Documentation: Describe all endpoints (/upload, /analysis/:id,
/optimize/:id), including request/response formats and examples (can use
Postman to generate this)
●​ Agent Design: Explanation of LangChain implementation
●​ Future Improvements: List at least 5 potential future improvements or features that
could be added to the project

Submission Details:
1. Email to (hello@aakaarai.<NAME_EMAIL>)
Send an email to the specified email addresses with:
●​ A link to the GitHub repository
●​ A link to the deployed application

2. Deadline:
●​ Deadline for the assignment is 3 days. After that, we will schedule a call to go over your
code and your thinking in detail

