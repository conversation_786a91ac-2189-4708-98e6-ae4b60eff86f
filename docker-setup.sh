#!/bin/bash

# AakaarAI Docker Setup Script
echo "🐳 Setting up AakaarAI with Docker..."
echo "====================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker is installed and running"

# Function to check if container exists
container_exists() {
    docker ps -a --format "table {{.Names}}" | grep -q "^$1$"
}

# Function to check if container is running
container_running() {
    docker ps --format "table {{.Names}}" | grep -q "^$1$"
}

# Setup MySQL container
echo ""
echo "🗄️  Setting up MySQL container..."
MYSQL_CONTAINER="mysql-aakaar"

if container_exists $MYSQL_CONTAINER; then
    if container_running $MYSQL_CONTAINER; then
        echo "✅ MySQL container is already running"
    else
        echo "📦 Starting existing MySQL container..."
        docker start $MYSQL_CONTAINER
        echo "✅ MySQL container started"
    fi
else
    echo "🐳 Creating new MySQL container..."
    docker run --name $MYSQL_CONTAINER \
        -e MYSQL_ROOT_PASSWORD=root \
        -e MYSQL_DATABASE=AakaarAI \
        -p 3306:3306 \
        -d mysql:latest
    
    echo "✅ MySQL container created and started"
    echo "⏳ Waiting for MySQL to be ready..."
    
    # Wait for MySQL to be ready
    for i in {1..30}; do
        if docker exec $MYSQL_CONTAINER mysqladmin ping -h localhost --silent; then
            echo "✅ MySQL is ready!"
            break
        fi
        echo "   Waiting... ($i/30)"
        sleep 2
    done
fi

# Setup Redis container
echo ""
echo "🔴 Setting up Redis container..."
REDIS_CONTAINER="redis-aakaar"

if container_exists $REDIS_CONTAINER; then
    if container_running $REDIS_CONTAINER; then
        echo "✅ Redis container is already running"
    else
        echo "📦 Starting existing Redis container..."
        docker start $REDIS_CONTAINER
        echo "✅ Redis container started"
    fi
else
    echo "🐳 Creating new Redis container..."
    docker run --name $REDIS_CONTAINER \
        -p 6379:6379 \
        -d redis:latest
    
    echo "✅ Redis container created and started"
fi

# Create database if it doesn't exist
echo ""
echo "🗄️  Setting up database..."
docker exec -i $MYSQL_CONTAINER mysql -u root -proot << EOF
CREATE DATABASE IF NOT EXISTS AakaarAI;
SHOW DATABASES;
EOF

if [ $? -eq 0 ]; then
    echo "✅ Database 'AakaarAI' is ready"
else
    echo "⚠️  Could not verify database creation"
fi

# Test connections
echo ""
echo "🔍 Testing connections..."

# Test MySQL
if docker exec $MYSQL_CONTAINER mysql -u root -proot -e "SELECT 1;" &> /dev/null; then
    echo "✅ MySQL connection successful"
else
    echo "❌ MySQL connection failed"
fi

# Test Redis
if docker exec $REDIS_CONTAINER redis-cli ping | grep -q PONG; then
    echo "✅ Redis connection successful"
else
    echo "❌ Redis connection failed"
fi

# Update .env file
echo ""
echo "📝 Updating environment configuration..."
if [ -f .env ]; then
    # Update existing .env file
    sed -i.bak 's/^DB_HOST=.*/DB_HOST=localhost/' .env
    sed -i.bak 's/^DB_PORT=.*/DB_PORT=3306/' .env
    sed -i.bak 's/^DB_USER=.*/DB_USER=root/' .env
    sed -i.bak 's/^DB_PASSWORD=.*/DB_PASSWORD=root/' .env
    sed -i.bak 's/^DB_NAME=.*/DB_NAME=AakaarAI/' .env
    sed -i.bak 's/^REDIS_HOST=.*/REDIS_HOST=localhost/' .env
    sed -i.bak 's/^REDIS_PORT=.*/REDIS_PORT=6379/' .env
    echo "✅ Environment file updated"
else
    # Create new .env file
    cp .env.example .env
    sed -i.bak 's/^DB_HOST=.*/DB_HOST=localhost/' .env
    sed -i.bak 's/^DB_PORT=.*/DB_PORT=3306/' .env
    sed -i.bak 's/^DB_USER=.*/DB_USER=root/' .env
    sed -i.bak 's/^DB_PASSWORD=.*/DB_PASSWORD=root/' .env
    sed -i.bak 's/^DB_NAME=.*/DB_NAME=AakaarAI/' .env
    sed -i.bak 's/^REDIS_HOST=.*/REDIS_HOST=localhost/' .env
    sed -i.bak 's/^REDIS_PORT=.*/REDIS_PORT=6379/' .env
    echo "✅ Environment file created and configured"
fi

# Clean up backup file
rm -f .env.bak

# Display container information
echo ""
echo "📊 Container Status"
echo "=================="
echo "MySQL Container: $MYSQL_CONTAINER"
docker ps --filter "name=$MYSQL_CONTAINER" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""
echo "Redis Container: $REDIS_CONTAINER"
docker ps --filter "name=$REDIS_CONTAINER" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Final instructions
echo ""
echo "🎉 Docker setup complete!"
echo "========================="
echo ""
echo "Your containers are running:"
echo "🗄️  MySQL: localhost:3306 (user: root, password: root, database: AakaarAI)"
echo "🔴 Redis: localhost:6379"
echo ""
echo "Next steps:"
echo "1. Install dependencies: ./install.sh"
echo "2. Start the application: ./start.sh"
echo ""
echo "Useful Docker commands:"
echo "📊 View containers: docker ps"
echo "🛑 Stop containers: docker stop $MYSQL_CONTAINER $REDIS_CONTAINER"
echo "🚀 Start containers: docker start $MYSQL_CONTAINER $REDIS_CONTAINER"
echo "🗑️  Remove containers: docker rm $MYSQL_CONTAINER $REDIS_CONTAINER"
echo ""
echo "Happy analyzing! 🚀"
