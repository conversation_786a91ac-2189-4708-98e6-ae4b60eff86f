{"name": "aakaar-ai-backend", "version": "1.0.0", "description": "Backend-focused web application with sophisticated data processing and AI agent capabilities for ad data analysis", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "workers": "node workers.js", "workers:dev": "nodemon workers.js", "test": "jest", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "setup": "npm install && cd frontend && npm install", "dev:full": "concurrently \"npm run dev\" \"npm run workers:dev\" \"cd frontend && npm start\"", "postinstall": "cd frontend && npm install"}, "keywords": ["nodejs", "express", "mysql", "sequelize", "langchain", "csv-analysis", "ai-agents"], "author": "AakaarAI", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "bullmq": "^4.15.4", "redis": "^4.6.11", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "csv-parse": "^5.5.2", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "morgan": "^1.10.0", "joi": "^17.11.0", "dotenv": "^16.3.1", "langchain": "^0.0.212", "@langchain/openai": "^0.0.14", "@langchain/community": "^0.0.20", "pdf-parse": "^1.1.1", "uuid": "^9.0.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}