const fs = require('fs');
const csv = require('csv-parse');
const { Transform } = require('stream');
const logger = require('../config/logger');

class CSVProcessor {
  constructor() {
    this.validationRules = {
      requiredFields: [
        'Matched product',
        'Impressions',
        'Clicks',
        'Spend(USD)',
        'Sales(USD)',
        'Orders'
      ],
      numericFields: [
        'Impressions',
        'Clicks',
        'Spend(USD)',
        'Sales(USD)',
        'Orders',
        'CTR',
        'CPC(USD)',
        'ACOS',
        'ROAS',
        'Conversion rate'
      ]
    };
  }

  // Parse CSV file and return processed data
  async processCSV(filePath, progressCallback = null) {
    return new Promise((resolve, reject) => {
      const results = [];
      const errors = [];
      let totalRows = 0;
      let processedRows = 0;
      let validRows = 0;

      // First pass: count total rows
      fs.createReadStream(filePath)
        .pipe(csv({ columns: true, skip_empty_lines: true }))
        .on('data', () => totalRows++)
        .on('end', () => {
          // Second pass: process data
          this.processData(filePath, totalRows, progressCallback)
            .then(result => resolve(result))
            .catch(error => reject(error));
        })
        .on('error', reject);
    });
  }

  async processData(filePath, totalRows, progressCallback) {
    return new Promise((resolve, reject) => {
      const results = [];
      const errors = [];
      let processedRows = 0;
      let validRows = 0;

      const transformStream = new Transform({
        objectMode: true,
        transform: (chunk, encoding, callback) => {
          processedRows++;
          
          try {
            const processedRow = this.processRow(chunk, processedRows);
            if (processedRow.isValid) {
              validRows++;
              results.push(processedRow.data);
            } else {
              errors.push({
                row: processedRows,
                errors: processedRow.errors,
                data: chunk
              });
            }

            // Report progress
            if (progressCallback && processedRows % 10 === 0) {
              const progress = Math.round((processedRows / totalRows) * 100);
              progressCallback(progress, processedRows, validRows, errors.length);
            }

            callback();
          } catch (error) {
            errors.push({
              row: processedRows,
              errors: [error.message],
              data: chunk
            });
            callback();
          }
        }
      });

      fs.createReadStream(filePath)
        .pipe(csv({ 
          columns: true, 
          skip_empty_lines: true,
          trim: true,
          cast: false // Keep all values as strings initially
        }))
        .pipe(transformStream)
        .on('finish', () => {
          // Final progress update
          if (progressCallback) {
            progressCallback(100, processedRows, validRows, errors.length);
          }

          resolve({
            data: results,
            summary: {
              totalRows: processedRows,
              validRows,
              invalidRows: errors.length,
              errors: errors.slice(0, 100) // Limit errors to first 100
            }
          });
        })
        .on('error', reject);
    });
  }

  processRow(row, rowNumber) {
    const errors = [];
    const processedData = {};

    // Normalize column names (remove quotes, trim spaces)
    const normalizedRow = {};
    Object.keys(row).forEach(key => {
      const normalizedKey = key.replace(/"/g, '').trim();
      normalizedRow[normalizedKey] = row[key];
    });

    // Check required fields
    this.validationRules.requiredFields.forEach(field => {
      if (!normalizedRow[field] || normalizedRow[field].trim() === '') {
        errors.push(`Missing required field: ${field}`);
      }
    });

    // Process and validate each field
    try {
      processedData.matchedProduct = this.cleanString(normalizedRow['Matched product '] || normalizedRow['Matched product']);
      processedData.productTargets = this.cleanString(normalizedRow['Product targets']);
      processedData.addedAs = this.cleanString(normalizedRow['Added as']);
      
      // Process numeric fields
      processedData.impressions = this.parseNumber(normalizedRow['Impressions'], 'Impressions', errors);
      processedData.clicks = this.parseNumber(normalizedRow['Clicks'], 'Clicks', errors);
      processedData.spend = this.parseNumber(normalizedRow['Spend(USD)'], 'Spend(USD)', errors);
      processedData.sales = this.parseNumber(normalizedRow['Sales(USD)'], 'Sales(USD)', errors);
      processedData.orders = this.parseNumber(normalizedRow['Orders'], 'Orders', errors);
      
      // Process percentage/ratio fields
      processedData.ctr = this.parseNumber(normalizedRow['CTR'], 'CTR', errors);
      processedData.cpc = this.parseNumber(normalizedRow['CPC(USD)'], 'CPC(USD)', errors);
      processedData.acos = this.parseNumber(normalizedRow['ACOS'], 'ACOS', errors);
      processedData.roas = this.parseNumber(normalizedRow['ROAS'], 'ROAS', errors);
      processedData.conversionRate = this.parseNumber(normalizedRow['Conversion rate'], 'Conversion rate', errors);

      // Calculate derived metrics if missing
      if (processedData.impressions > 0 && !processedData.ctr) {
        processedData.ctr = (processedData.clicks / processedData.impressions) * 100;
      }

      if (processedData.clicks > 0 && !processedData.cpc) {
        processedData.cpc = processedData.spend / processedData.clicks;
      }

      if (processedData.sales > 0 && !processedData.acos) {
        processedData.acos = (processedData.spend / processedData.sales) * 100;
      }

      if (processedData.spend > 0 && !processedData.roas) {
        processedData.roas = processedData.sales / processedData.spend;
      }

      if (processedData.clicks > 0 && !processedData.conversionRate) {
        processedData.conversionRate = (processedData.orders / processedData.clicks) * 100;
      }

      // Add metadata
      processedData.rowNumber = rowNumber;
      processedData.processedAt = new Date();

    } catch (error) {
      errors.push(`Processing error: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      data: processedData,
      errors
    };
  }

  parseNumber(value, fieldName, errors) {
    if (!value || value === '' || value === 'null' || value === 'undefined') {
      return 0;
    }

    // Remove any non-numeric characters except decimal point and minus sign
    const cleanValue = String(value).replace(/[^0-9.-]/g, '');
    const parsed = parseFloat(cleanValue);

    if (isNaN(parsed)) {
      errors.push(`Invalid number format for ${fieldName}: ${value}`);
      return 0;
    }

    // Validate ranges
    if (parsed < 0 && !['ACOS', 'ROAS'].includes(fieldName)) {
      errors.push(`${fieldName} cannot be negative: ${parsed}`);
      return 0;
    }

    return parsed;
  }

  cleanString(value) {
    if (!value) return '';
    return String(value).replace(/"/g, '').trim();
  }

  // Validate CSV structure
  validateCSVStructure(filePath) {
    return new Promise((resolve, reject) => {
      const stream = fs.createReadStream(filePath);
      let headerProcessed = false;
      let headers = [];

      stream
        .pipe(csv({ columns: false, max_record_size: 1000000 }))
        .on('data', (row) => {
          if (!headerProcessed) {
            headers = row.map(header => header.replace(/"/g, '').trim());
            headerProcessed = true;
            
            // Check if required columns exist
            const missingColumns = this.validationRules.requiredFields.filter(
              field => !headers.some(header => header.includes(field.replace(/[()]/g, '')))
            );

            if (missingColumns.length > 0) {
              return reject(new Error(`Missing required columns: ${missingColumns.join(', ')}`));
            }

            resolve({
              isValid: true,
              headers,
              message: 'CSV structure is valid'
            });
          }
        })
        .on('error', reject);
    });
  }

  // Get sample data from CSV
  async getSampleData(filePath, sampleSize = 5) {
    return new Promise((resolve, reject) => {
      const samples = [];
      let count = 0;

      fs.createReadStream(filePath)
        .pipe(csv({ columns: true, skip_empty_lines: true }))
        .on('data', (row) => {
          if (count < sampleSize) {
            samples.push(row);
            count++;
          }
        })
        .on('end', () => {
          resolve(samples);
        })
        .on('error', reject);
    });
  }
}

module.exports = CSVProcessor;
