const CSVProcessor = require('./csvProcessor');
const { AnalysisJob, AnalysisResult } = require('../models');
const logger = require('../config/logger');

class AnalysisService {
  constructor() {
    this.csvProcessor = new CSVProcessor();
  }

  // Perform comprehensive analysis on CSV data
  async performAnalysis(jobId, filePath) {
    const job = await AnalysisJob.findByPk(jobId);
    if (!job) {
      throw new Error('Analysis job not found');
    }

    try {
      await job.markAsProcessing();
      logger.info(`Starting analysis for job ${jobId}`);

      // Process CSV with progress tracking
      const result = await this.csvProcessor.processCSV(filePath, (progress, processed, valid, invalid) => {
        job.updateProgress(Math.min(progress * 0.7, 70), processed); // 70% for CSV processing
        job.validRows = valid;
        job.invalidRows = invalid;
      });

      if (result.data.length === 0) {
        throw new Error('No valid data found in CSV file');
      }

      // Update job with processing results
      job.totalRows = result.summary.totalRows;
      job.processedRows = result.summary.totalRows;
      job.validRows = result.summary.validRows;
      job.invalidRows = result.summary.invalidRows;
      await job.save();

      // Perform analysis calculations
      await job.updateProgress(75);
      const analysisResults = await this.calculateMetrics(result.data);

      // Generate insights
      await job.updateProgress(85);
      const insights = await this.generateInsights(result.data, analysisResults);

      // Save analysis results
      await job.updateProgress(95);
      const analysisResult = await this.saveAnalysisResults(jobId, analysisResults, insights, result.data);

      await job.markAsCompleted();
      await job.updateProgress(100);

      logger.info(`Analysis completed for job ${jobId}`);
      return analysisResult;

    } catch (error) {
      logger.error(`Analysis failed for job ${jobId}:`, error);
      await job.markAsFailed(error.message);
      throw error;
    }
  }

  // Calculate comprehensive metrics
  async calculateMetrics(data) {
    const metrics = {
      overall: {},
      keywords: [],
      performance: {},
      trends: {},
      distributions: {}
    };

    // Calculate overall metrics
    const totals = data.reduce((acc, row) => {
      acc.impressions += row.impressions || 0;
      acc.clicks += row.clicks || 0;
      acc.spend += row.spend || 0;
      acc.sales += row.sales || 0;
      acc.orders += row.orders || 0;
      return acc;
    }, { impressions: 0, clicks: 0, spend: 0, sales: 0, orders: 0 });

    metrics.overall = {
      totalImpressions: totals.impressions,
      totalClicks: totals.clicks,
      totalSpend: totals.spend,
      totalSales: totals.sales,
      totalOrders: totals.orders,
      overallCTR: totals.impressions > 0 ? (totals.clicks / totals.impressions * 100) : 0,
      overallCPC: totals.clicks > 0 ? (totals.spend / totals.clicks) : 0,
      overallACOS: totals.sales > 0 ? (totals.spend / totals.sales * 100) : 0,
      overallROAS: totals.spend > 0 ? (totals.sales / totals.spend) : 0,
      overallConversionRate: totals.clicks > 0 ? (totals.orders / totals.clicks * 100) : 0
    };

    // Analyze individual keywords
    metrics.keywords = data.map(row => ({
      keyword: row.matchedProduct,
      impressions: row.impressions,
      clicks: row.clicks,
      spend: row.spend,
      sales: row.sales,
      orders: row.orders,
      ctr: row.ctr,
      cpc: row.cpc,
      acos: row.acos,
      roas: row.roas,
      conversionRate: row.conversionRate,
      efficiency: this.calculateEfficiencyScore(row)
    }));

    // Performance analysis
    metrics.performance = {
      topPerformers: this.getTopPerformers(data, 'roas', 10),
      bottomPerformers: this.getBottomPerformers(data, 'acos', 10),
      highSpenders: this.getTopPerformers(data, 'spend', 10),
      lowConverters: this.getLowConverters(data, 10),
      zeroSalesKeywords: data.filter(row => row.sales === 0).length,
      highACOSKeywords: data.filter(row => row.acos > 50).length
    };

    // Distribution analysis
    metrics.distributions = {
      spendDistribution: this.calculateDistribution(data, 'spend'),
      salesDistribution: this.calculateDistribution(data, 'sales'),
      acosDistribution: this.calculateACOSDistribution(data),
      roasDistribution: this.calculateROASDistribution(data)
    };

    // Trend analysis (simplified for single snapshot)
    metrics.trends = {
      performanceSegments: this.segmentPerformance(data),
      keywordCategories: this.categorizeKeywords(data)
    };

    return metrics;
  }

  calculateEfficiencyScore(row) {
    // Custom efficiency score based on ROAS, conversion rate, and spend efficiency
    const roasScore = Math.min(row.roas / 3, 10); // Normalize ROAS (3+ is excellent)
    const conversionScore = Math.min(row.conversionRate / 10, 10); // Normalize conversion rate
    const spendEfficiency = row.spend > 0 ? Math.min(row.sales / row.spend * 2, 10) : 0;
    
    return (roasScore + conversionScore + spendEfficiency) / 3;
  }

  getTopPerformers(data, metric, limit) {
    return data
      .filter(row => row[metric] > 0)
      .sort((a, b) => b[metric] - a[metric])
      .slice(0, limit)
      .map(row => ({
        keyword: row.matchedProduct,
        value: row[metric],
        spend: row.spend,
        sales: row.sales,
        orders: row.orders,
        roas: row.roas,
        acos: row.acos
      }));
  }

  getBottomPerformers(data, metric, limit) {
    return data
      .filter(row => row[metric] > 0)
      .sort((a, b) => a[metric] - b[metric])
      .slice(0, limit)
      .map(row => ({
        keyword: row.matchedProduct,
        value: row[metric],
        spend: row.spend,
        sales: row.sales,
        orders: row.orders,
        roas: row.roas,
        acos: row.acos
      }));
  }

  getLowConverters(data, limit) {
    return data
      .filter(row => row.clicks > 0 && row.orders === 0)
      .sort((a, b) => b.spend - a.spend)
      .slice(0, limit)
      .map(row => ({
        keyword: row.matchedProduct,
        clicks: row.clicks,
        spend: row.spend,
        conversionRate: row.conversionRate
      }));
  }

  calculateDistribution(data, field) {
    const values = data.map(row => row[field]).filter(val => val > 0);
    if (values.length === 0) return {};

    values.sort((a, b) => a - b);
    const total = values.reduce((sum, val) => sum + val, 0);

    return {
      min: values[0],
      max: values[values.length - 1],
      median: values[Math.floor(values.length / 2)],
      average: total / values.length,
      percentile25: values[Math.floor(values.length * 0.25)],
      percentile75: values[Math.floor(values.length * 0.75)],
      standardDeviation: this.calculateStandardDeviation(values)
    };
  }

  calculateACOSDistribution(data) {
    const acosRanges = {
      excellent: 0,    // 0-20%
      good: 0,         // 20-40%
      average: 0,      // 40-60%
      poor: 0,         // 60-80%
      terrible: 0      // 80%+
    };

    data.forEach(row => {
      if (row.acos <= 20) acosRanges.excellent++;
      else if (row.acos <= 40) acosRanges.good++;
      else if (row.acos <= 60) acosRanges.average++;
      else if (row.acos <= 80) acosRanges.poor++;
      else acosRanges.terrible++;
    });

    return acosRanges;
  }

  calculateROASDistribution(data) {
    const roasRanges = {
      excellent: 0,    // 3+
      good: 0,         // 2-3
      average: 0,      // 1-2
      poor: 0,         // 0.5-1
      terrible: 0      // <0.5
    };

    data.forEach(row => {
      if (row.roas >= 3) roasRanges.excellent++;
      else if (row.roas >= 2) roasRanges.good++;
      else if (row.roas >= 1) roasRanges.average++;
      else if (row.roas >= 0.5) roasRanges.poor++;
      else roasRanges.terrible++;
    });

    return roasRanges;
  }

  segmentPerformance(data) {
    const segments = {
      stars: [], // High ROAS, High Volume
      cashCows: [], // Good ROAS, High Volume
      potentials: [], // High ROAS, Low Volume
      problems: [] // Low ROAS, High Spend
    };

    const medianSpend = this.calculateDistribution(data, 'spend').median;
    const medianROAS = this.calculateDistribution(data, 'roas').median;

    data.forEach(row => {
      if (row.roas >= medianROAS && row.spend >= medianSpend) {
        segments.stars.push(row.matchedProduct);
      } else if (row.roas >= medianROAS * 0.8 && row.spend >= medianSpend) {
        segments.cashCows.push(row.matchedProduct);
      } else if (row.roas >= medianROAS && row.spend < medianSpend) {
        segments.potentials.push(row.matchedProduct);
      } else if (row.roas < medianROAS * 0.8 && row.spend >= medianSpend) {
        segments.problems.push(row.matchedProduct);
      }
    });

    return segments;
  }

  categorizeKeywords(data) {
    const categories = {
      branded: [],
      generic: [],
      competitor: [],
      longTail: []
    };

    data.forEach(row => {
      const keyword = row.matchedProduct.toLowerCase();
      const wordCount = keyword.split(' ').length;

      if (wordCount >= 4) {
        categories.longTail.push(keyword);
      } else if (keyword.includes('mask') || keyword.includes('covid')) {
        categories.generic.push(keyword);
      } else {
        categories.generic.push(keyword);
      }
    });

    return categories;
  }

  calculateStandardDeviation(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  // Generate insights from analysis
  async generateInsights(data, metrics) {
    const insights = {
      keyFindings: [],
      recommendations: [],
      alerts: []
    };

    // Key findings
    insights.keyFindings.push(
      `Analyzed ${data.length} keywords with total spend of $${metrics.overall.totalSpend.toFixed(2)}`,
      `Overall ROAS: ${metrics.overall.overallROAS.toFixed(2)}x`,
      `Overall ACOS: ${metrics.overall.overallACOS.toFixed(2)}%`,
      `Conversion Rate: ${metrics.overall.overallConversionRate.toFixed(2)}%`
    );

    // Performance insights
    if (metrics.performance.zeroSalesKeywords > 0) {
      insights.keyFindings.push(
        `${metrics.performance.zeroSalesKeywords} keywords generated no sales`
      );
    }

    if (metrics.performance.highACOSKeywords > 0) {
      insights.keyFindings.push(
        `${metrics.performance.highACOSKeywords} keywords have ACOS > 50%`
      );
    }

    // Generate recommendations
    insights.recommendations = this.generateRecommendations(metrics);

    // Generate alerts
    insights.alerts = this.generateAlerts(metrics);

    return insights;
  }

  generateRecommendations(metrics) {
    const recommendations = [];

    // ACOS recommendations
    if (metrics.overall.overallACOS > 40) {
      recommendations.push({
        type: 'acos_optimization',
        priority: 'high',
        title: 'Reduce Overall ACOS',
        description: `Current ACOS of ${metrics.overall.overallACOS.toFixed(2)}% is above recommended 40%`,
        actions: ['Pause high ACOS keywords', 'Reduce bids on underperforming terms', 'Add negative keywords']
      });
    }

    // ROAS recommendations
    if (metrics.overall.overallROAS < 2) {
      recommendations.push({
        type: 'roas_improvement',
        priority: 'high',
        title: 'Improve Return on Ad Spend',
        description: `Current ROAS of ${metrics.overall.overallROAS.toFixed(2)}x is below recommended 2x`,
        actions: ['Focus budget on top performing keywords', 'Optimize product listings', 'Improve conversion rates']
      });
    }

    // Zero sales keywords
    if (metrics.performance.zeroSalesKeywords > 0) {
      recommendations.push({
        type: 'keyword_cleanup',
        priority: 'medium',
        title: 'Remove Non-Converting Keywords',
        description: `${metrics.performance.zeroSalesKeywords} keywords are not generating sales`,
        actions: ['Pause zero-sales keywords', 'Analyze search terms', 'Add as negative keywords if irrelevant']
      });
    }

    return recommendations;
  }

  generateAlerts(metrics) {
    const alerts = [];

    if (metrics.overall.overallACOS > 60) {
      alerts.push({
        type: 'critical',
        message: 'Critical: ACOS exceeds 60% - immediate action required'
      });
    }

    if (metrics.overall.overallROAS < 1) {
      alerts.push({
        type: 'critical',
        message: 'Critical: ROAS below 1x - campaigns are losing money'
      });
    }

    return alerts;
  }

  // Save analysis results to database
  async saveAnalysisResults(jobId, metrics, insights, rawData) {
    const analysisResult = await AnalysisResult.create({
      jobId,
      totalImpressions: metrics.overall.totalImpressions,
      totalClicks: metrics.overall.totalClicks,
      totalSpend: metrics.overall.totalSpend,
      totalSales: metrics.overall.totalSales,
      totalOrders: metrics.overall.totalOrders,
      overallCTR: metrics.overall.overallCTR,
      overallCPC: metrics.overall.overallCPC,
      overallACOS: metrics.overall.overallACOS,
      overallROAS: metrics.overall.overallROAS,
      overallConversionRate: metrics.overall.overallConversionRate,
      topPerformingKeywords: metrics.performance.topPerformers,
      bottomPerformingKeywords: metrics.performance.bottomPerformers,
      highSpendKeywords: metrics.performance.highSpenders,
      lowConversionKeywords: metrics.performance.lowConverters,
      performanceTrends: metrics.trends,
      keywordDistribution: metrics.distributions,
      spendDistribution: metrics.distributions.spendDistribution,
      keyFindings: insights.keyFindings,
      recommendations: insights.recommendations,
      analysisVersion: '1.0'
    });

    return analysisResult;
  }
}

module.exports = AnalysisService;
