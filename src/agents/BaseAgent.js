const { ChatOpenAI } = require('@langchain/openai');
const { PromptTemplate } = require('langchain/prompts');
const { LLMChain } = require('langchain/chains');
const logger = require('../config/logger');

class BaseAgent {
  constructor(options = {}) {
    this.name = options.name || 'BaseAgent';
    this.temperature = options.temperature || 0.7;
    this.maxTokens = options.maxTokens || 2000;
    this.model = options.model || 'gpt-3.5-turbo';
    
    // Initialize LLM
    this.llm = new ChatOpenAI({
      openAIApiKey: process.env.OPENAI_API_KEY,
      modelName: this.model,
      temperature: this.temperature,
      maxTokens: this.maxTokens,
    });

    this.memory = new Map(); // Simple memory store
    this.context = {};
  }

  // Set context for the agent
  setContext(context) {
    this.context = { ...this.context, ...context };
  }

  // Store information in memory
  remember(key, value) {
    this.memory.set(key, value);
  }

  // Retrieve information from memory
  recall(key) {
    return this.memory.get(key);
  }

  // Clear memory
  clearMemory() {
    this.memory.clear();
  }

  // Create a prompt template
  createPrompt(template, inputVariables) {
    return new PromptTemplate({
      template,
      inputVariables,
    });
  }

  // Create an LLM chain
  createChain(prompt) {
    return new LLMChain({
      llm: this.llm,
      prompt,
    });
  }

  // Execute a chain with error handling
  async executeChain(chain, inputs) {
    try {
      logger.debug(`${this.name} executing chain with inputs:`, inputs);
      const result = await chain.call(inputs);
      logger.debug(`${this.name} chain result:`, result);
      return result;
    } catch (error) {
      logger.error(`${this.name} chain execution failed:`, error);
      throw new Error(`Agent ${this.name} failed: ${error.message}`);
    }
  }

  // Format data for LLM consumption
  formatDataForLLM(data) {
    if (Array.isArray(data)) {
      return data.map(item => this.formatObjectForLLM(item)).join('\n');
    }
    return this.formatObjectForLLM(data);
  }

  formatObjectForLLM(obj) {
    return Object.entries(obj)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
  }

  // Parse structured output from LLM
  parseStructuredOutput(text, format = 'json') {
    try {
      if (format === 'json') {
        // Extract JSON from text
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      }
      return text;
    } catch (error) {
      logger.warn(`${this.name} failed to parse structured output:`, error);
      return text;
    }
  }

  // Validate required inputs
  validateInputs(inputs, required) {
    const missing = required.filter(field => !inputs[field]);
    if (missing.length > 0) {
      throw new Error(`Missing required inputs: ${missing.join(', ')}`);
    }
  }

  // Abstract method to be implemented by subclasses
  async process(inputs) {
    throw new Error('Process method must be implemented by subclass');
  }

  // Health check for the agent
  async healthCheck() {
    try {
      const testPrompt = this.createPrompt('Say "OK" if you can respond.', []);
      const testChain = this.createChain(testPrompt);
      const result = await this.executeChain(testChain, {});
      return result.text.includes('OK');
    } catch (error) {
      logger.error(`${this.name} health check failed:`, error);
      return false;
    }
  }

  // Get agent information
  getInfo() {
    return {
      name: this.name,
      model: this.model,
      temperature: this.temperature,
      maxTokens: this.maxTokens,
      memorySize: this.memory.size,
      contextKeys: Object.keys(this.context)
    };
  }
}

module.exports = BaseAgent;
