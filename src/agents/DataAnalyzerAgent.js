const BaseAgent = require('./BaseAgent');
const logger = require('../config/logger');

class DataAnalyzerAgent extends BaseAgent {
  constructor() {
    super({
      name: 'DataAnalyzerAgent',
      temperature: 0.3, // Lower temperature for more consistent analysis
      maxTokens: 3000,
      model: 'gpt-3.5-turbo'
    });

    this.analysisPrompt = this.createPrompt(`
You are an expert data analyst specializing in Amazon PPC advertising data analysis. 
Analyze the provided advertising data and identify key patterns, anomalies, and insights.

Data Summary:
- Total Keywords: {totalKeywords}
- Total Spend: ${totalSpend}
- Total Sales: ${totalSales}
- Overall ROAS: {overallROAS}x
- Overall ACOS: {overallACOS}%

Top Performing Keywords:
{topKeywords}

Bottom Performing Keywords:
{bottomKeywords}

High Spend Keywords:
{highSpendKeywords}

Performance Distribution:
{performanceDistribution}

Please provide a comprehensive analysis including:
1. Key performance patterns identified
2. Anomalies or concerning trends
3. Data quality observations
4. Statistical insights
5. Performance segmentation analysis

Format your response as a structured analysis with clear sections.
`, [
      'totalKeywords',
      'totalSpend', 
      'totalSales',
      'overallROAS',
      'overallACOS',
      'topKeywords',
      'bottomKeywords',
      'highSpendKeywords',
      'performanceDistribution'
    ]);

    this.anomalyDetectionPrompt = this.createPrompt(`
Analyze the following advertising data for anomalies and unusual patterns:

Data: {data}

Look for:
1. Keywords with unusually high spend but low sales
2. Keywords with high click volume but zero conversions
3. Extreme ACOS values (very high or suspiciously low)
4. Inconsistent performance patterns
5. Potential data quality issues

Provide specific examples and explain why each is considered an anomaly.
Return your findings in JSON format with the following structure:
{
  "anomalies": [
    {
      "type": "high_spend_low_sales",
      "keyword": "keyword name",
      "description": "explanation",
      "severity": "high|medium|low",
      "recommendation": "suggested action"
    }
  ],
  "summary": "overall summary of anomalies found"
}
`, ['data']);
  }

  async process(inputs) {
    this.validateInputs(inputs, ['analysisData', 'metrics']);
    
    const { analysisData, metrics } = inputs;
    
    try {
      // Perform comprehensive data analysis
      const analysis = await this.performDataAnalysis(analysisData, metrics);
      
      // Detect anomalies
      const anomalies = await this.detectAnomalies(analysisData);
      
      // Generate insights
      const insights = await this.generateInsights(analysis, anomalies);
      
      return {
        success: true,
        analysis,
        anomalies,
        insights,
        processedAt: new Date().toISOString()
      };
      
    } catch (error) {
      logger.error('DataAnalyzerAgent processing failed:', error);
      throw error;
    }
  }

  async performDataAnalysis(data, metrics) {
    const chain = this.createChain(this.analysisPrompt);
    
    const inputs = {
      totalKeywords: data.length,
      totalSpend: metrics.overall.totalSpend.toFixed(2),
      totalSales: metrics.overall.totalSales.toFixed(2),
      overallROAS: metrics.overall.overallROAS.toFixed(2),
      overallACOS: metrics.overall.overallACOS.toFixed(2),
      topKeywords: this.formatKeywordList(metrics.performance.topPerformers),
      bottomKeywords: this.formatKeywordList(metrics.performance.bottomPerformers),
      highSpendKeywords: this.formatKeywordList(metrics.performance.highSpenders),
      performanceDistribution: this.formatDistribution(metrics.distributions)
    };

    const result = await this.executeChain(chain, inputs);
    return result.text;
  }

  async detectAnomalies(data) {
    const chain = this.createChain(this.anomalyDetectionPrompt);
    
    // Sample data for anomaly detection (limit to prevent token overflow)
    const sampleData = data.slice(0, 50).map(row => ({
      keyword: row.matchedProduct,
      spend: row.spend,
      sales: row.sales,
      clicks: row.clicks,
      orders: row.orders,
      acos: row.acos,
      roas: row.roas
    }));

    const result = await this.executeChain(chain, {
      data: JSON.stringify(sampleData, null, 2)
    });

    return this.parseStructuredOutput(result.text, 'json');
  }

  async generateInsights(analysis, anomalies) {
    const insights = {
      dataQuality: this.assessDataQuality(anomalies),
      performanceInsights: this.extractPerformanceInsights(analysis),
      recommendations: this.generateDataRecommendations(anomalies),
      riskFactors: this.identifyRiskFactors(anomalies)
    };

    return insights;
  }

  assessDataQuality(anomalies) {
    if (!anomalies || !anomalies.anomalies) {
      return { score: 85, issues: [], status: 'good' };
    }

    const issues = anomalies.anomalies || [];
    const highSeverityCount = issues.filter(a => a.severity === 'high').length;
    const mediumSeverityCount = issues.filter(a => a.severity === 'medium').length;
    
    let score = 100;
    score -= highSeverityCount * 15;
    score -= mediumSeverityCount * 8;
    score -= (issues.length - highSeverityCount - mediumSeverityCount) * 3;
    
    score = Math.max(0, score);
    
    let status = 'excellent';
    if (score < 60) status = 'poor';
    else if (score < 75) status = 'fair';
    else if (score < 90) status = 'good';

    return {
      score,
      status,
      issues: issues.map(a => a.description),
      totalAnomalies: issues.length,
      highSeverityAnomalies: highSeverityCount
    };
  }

  extractPerformanceInsights(analysis) {
    // Extract key insights from the analysis text
    const insights = [];
    
    if (analysis.includes('high ACOS') || analysis.includes('poor performance')) {
      insights.push('Performance optimization needed for high ACOS keywords');
    }
    
    if (analysis.includes('zero sales') || analysis.includes('no conversions')) {
      insights.push('Multiple keywords generating clicks but no sales');
    }
    
    if (analysis.includes('top performer') || analysis.includes('excellent ROAS')) {
      insights.push('Strong performing keywords identified for scaling');
    }

    return insights;
  }

  generateDataRecommendations(anomalies) {
    const recommendations = [];
    
    if (anomalies && anomalies.anomalies) {
      anomalies.anomalies.forEach(anomaly => {
        if (anomaly.recommendation) {
          recommendations.push({
            type: anomaly.type,
            action: anomaly.recommendation,
            priority: anomaly.severity
          });
        }
      });
    }

    return recommendations;
  }

  identifyRiskFactors(anomalies) {
    const risks = [];
    
    if (anomalies && anomalies.anomalies) {
      const highSpendLowSales = anomalies.anomalies.filter(a => 
        a.type === 'high_spend_low_sales' && a.severity === 'high'
      );
      
      if (highSpendLowSales.length > 0) {
        risks.push({
          type: 'budget_waste',
          description: `${highSpendLowSales.length} keywords with high spend but low returns`,
          impact: 'high'
        });
      }
    }

    return risks;
  }

  formatKeywordList(keywords) {
    if (!keywords || keywords.length === 0) return 'None';
    
    return keywords.slice(0, 5).map(k => 
      `${k.keyword} (ROAS: ${k.roas?.toFixed(2) || 'N/A'}, Spend: $${k.spend?.toFixed(2) || '0'})`
    ).join('\n');
  }

  formatDistribution(distributions) {
    if (!distributions) return 'No distribution data available';
    
    const spend = distributions.spendDistribution || {};
    const acos = distributions.acosDistribution || {};
    
    return `
Spend Distribution:
- Average: $${spend.average?.toFixed(2) || '0'}
- Median: $${spend.median?.toFixed(2) || '0'}
- Max: $${spend.max?.toFixed(2) || '0'}

ACOS Distribution:
- Excellent (0-20%): ${acos.excellent || 0} keywords
- Good (20-40%): ${acos.good || 0} keywords
- Poor (60%+): ${acos.poor + acos.terrible || 0} keywords
    `.trim();
  }
}

module.exports = DataAnalyzerAgent;
