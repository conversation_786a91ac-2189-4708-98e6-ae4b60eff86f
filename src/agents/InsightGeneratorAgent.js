const BaseAgent = require('./BaseAgent');
const logger = require('../config/logger');

class InsightGeneratorAgent extends BaseAgent {
  constructor() {
    super({
      name: 'InsightGeneratorAgent',
      temperature: 0.7, // Higher temperature for creative insights
      maxTokens: 3000,
      model: 'gpt-3.5-turbo'
    });

    this.insightPrompt = this.createPrompt(`
You are an expert Amazon PPC strategist with 10+ years of experience. Generate actionable insights from the advertising data analysis.

Analysis Results:
{analysisResults}

Data Anomalies:
{anomalies}

Performance Metrics:
- Total Spend: ${totalSpend}
- Total Sales: ${totalSales}
- ROAS: {roas}x
- ACOS: {acos}%
- Conversion Rate: {conversionRate}%

Generate comprehensive insights including:

1. EXECUTIVE SUMMARY (2-3 sentences)
   - Overall campaign health
   - Key opportunities identified

2. KEY FINDINGS (3-5 bullet points)
   - Most important discoveries
   - Performance highlights and concerns

3. STRATEGIC INSIGHTS (3-4 insights)
   - Deeper analysis of patterns
   - Market and competitive implications
   - Customer behavior insights

4. IMMEDIATE OPPORTUNITIES (2-3 opportunities)
   - Quick wins available
   - High-impact, low-effort improvements

5. RISK ASSESSMENT
   - Current risks to campaign performance
   - Potential budget waste areas

Make insights specific, actionable, and business-focused. Use data to support each insight.
`, [
      'analysisResults',
      'anomalies', 
      'totalSpend',
      'totalSales',
      'roas',
      'acos',
      'conversionRate'
    ]);

    this.trendAnalysisPrompt = this.createPrompt(`
Analyze the performance trends and patterns in this advertising data:

Performance Data:
{performanceData}

Keyword Segments:
{keywordSegments}

Identify and explain:
1. Performance trends across different keyword types
2. Spending efficiency patterns
3. Conversion behavior insights
4. Seasonal or temporal patterns (if any)
5. Keyword lifecycle stages

Provide trend analysis in a structured format with specific examples from the data.
`, ['performanceData', 'keywordSegments']);

    this.competitiveInsightPrompt = this.createPrompt(`
Based on the keyword performance data, generate competitive and market insights:

Top Performing Keywords: {topKeywords}
Underperforming Keywords: {underperformingKeywords}
Search Terms: {searchTerms}

Analyze:
1. Market positioning opportunities
2. Competitive gaps or advantages
3. Customer search intent patterns
4. Product positioning insights
5. Market expansion opportunities

Focus on actionable competitive intelligence and market opportunities.
`, ['topKeywords', 'underperformingKeywords', 'searchTerms']);
  }

  async process(inputs) {
    this.validateInputs(inputs, ['analysisResults', 'metrics', 'anomalies']);
    
    const { analysisResults, metrics, anomalies, rawData } = inputs;
    
    try {
      // Generate main insights
      const mainInsights = await this.generateMainInsights(analysisResults, metrics, anomalies);
      
      // Generate trend analysis
      const trendInsights = await this.generateTrendAnalysis(metrics, rawData);
      
      // Generate competitive insights
      const competitiveInsights = await this.generateCompetitiveInsights(metrics);
      
      // Combine and structure all insights
      const structuredInsights = this.structureInsights(mainInsights, trendInsights, competitiveInsights);
      
      return {
        success: true,
        insights: structuredInsights,
        generatedAt: new Date().toISOString()
      };
      
    } catch (error) {
      logger.error('InsightGeneratorAgent processing failed:', error);
      throw error;
    }
  }

  async generateMainInsights(analysisResults, metrics, anomalies) {
    const chain = this.createChain(this.insightPrompt);
    
    const inputs = {
      analysisResults: typeof analysisResults === 'string' ? analysisResults : JSON.stringify(analysisResults),
      anomalies: JSON.stringify(anomalies),
      totalSpend: metrics.overall.totalSpend.toFixed(2),
      totalSales: metrics.overall.totalSales.toFixed(2),
      roas: metrics.overall.overallROAS.toFixed(2),
      acos: metrics.overall.overallACOS.toFixed(2),
      conversionRate: metrics.overall.overallConversionRate.toFixed(2)
    };

    const result = await this.executeChain(chain, inputs);
    return this.parseInsights(result.text);
  }

  async generateTrendAnalysis(metrics, rawData) {
    const chain = this.createChain(this.trendAnalysisPrompt);
    
    const performanceData = this.summarizePerformanceData(metrics);
    const keywordSegments = this.analyzeKeywordSegments(rawData);
    
    const result = await this.executeChain(chain, {
      performanceData: JSON.stringify(performanceData),
      keywordSegments: JSON.stringify(keywordSegments)
    });

    return result.text;
  }

  async generateCompetitiveInsights(metrics) {
    const chain = this.createChain(this.competitiveInsightPrompt);
    
    const inputs = {
      topKeywords: this.formatKeywordList(metrics.performance.topPerformers),
      underperformingKeywords: this.formatKeywordList(metrics.performance.bottomPerformers),
      searchTerms: this.extractSearchTerms(metrics.performance.topPerformers)
    };

    const result = await this.executeChain(chain, inputs);
    return result.text;
  }

  parseInsights(insightText) {
    const sections = {
      executiveSummary: '',
      keyFindings: [],
      strategicInsights: [],
      immediateOpportunities: [],
      riskAssessment: ''
    };

    try {
      // Parse executive summary
      const execMatch = insightText.match(/EXECUTIVE SUMMARY[:\s]*(.*?)(?=\n\d\.|\nKEY FINDINGS|$)/is);
      if (execMatch) {
        sections.executiveSummary = execMatch[1].trim();
      }

      // Parse key findings
      const findingsMatch = insightText.match(/KEY FINDINGS[:\s]*(.*?)(?=\n\d\.|\nSTRATEGIC INSIGHTS|$)/is);
      if (findingsMatch) {
        sections.keyFindings = this.extractBulletPoints(findingsMatch[1]);
      }

      // Parse strategic insights
      const strategicMatch = insightText.match(/STRATEGIC INSIGHTS[:\s]*(.*?)(?=\n\d\.|\nIMMEDIATE OPPORTUNITIES|$)/is);
      if (strategicMatch) {
        sections.strategicInsights = this.extractBulletPoints(strategicMatch[1]);
      }

      // Parse immediate opportunities
      const opportunitiesMatch = insightText.match(/IMMEDIATE OPPORTUNITIES[:\s]*(.*?)(?=\n\d\.|\nRISK ASSESSMENT|$)/is);
      if (opportunitiesMatch) {
        sections.immediateOpportunities = this.extractBulletPoints(opportunitiesMatch[1]);
      }

      // Parse risk assessment
      const riskMatch = insightText.match(/RISK ASSESSMENT[:\s]*(.*?)$/is);
      if (riskMatch) {
        sections.riskAssessment = riskMatch[1].trim();
      }

    } catch (error) {
      logger.warn('Failed to parse structured insights, returning raw text');
      sections.executiveSummary = insightText;
    }

    return sections;
  }

  extractBulletPoints(text) {
    return text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && (line.startsWith('-') || line.startsWith('•') || line.match(/^\d+\./)))
      .map(line => line.replace(/^[-•\d.]\s*/, '').trim())
      .filter(line => line.length > 10); // Filter out very short lines
  }

  summarizePerformanceData(metrics) {
    return {
      overallMetrics: metrics.overall,
      topPerformers: metrics.performance.topPerformers.slice(0, 5),
      bottomPerformers: metrics.performance.bottomPerformers.slice(0, 5),
      distributions: metrics.distributions,
      segments: metrics.trends.performanceSegments
    };
  }

  analyzeKeywordSegments(rawData) {
    if (!rawData || rawData.length === 0) return {};

    const segments = {
      highVolume: rawData.filter(row => row.impressions > 100),
      highSpend: rawData.filter(row => row.spend > 10),
      highConversion: rawData.filter(row => row.conversionRate > 5),
      zeroSales: rawData.filter(row => row.sales === 0),
      branded: rawData.filter(row => this.isBrandedKeyword(row.matchedProduct)),
      longTail: rawData.filter(row => row.matchedProduct.split(' ').length >= 4)
    };

    return {
      highVolume: segments.highVolume.length,
      highSpend: segments.highSpend.length,
      highConversion: segments.highConversion.length,
      zeroSales: segments.zeroSales.length,
      branded: segments.branded.length,
      longTail: segments.longTail.length
    };
  }

  isBrandedKeyword(keyword) {
    const brandTerms = ['brand', 'company', 'official', 'original'];
    return brandTerms.some(term => keyword.toLowerCase().includes(term));
  }

  extractSearchTerms(topKeywords) {
    return topKeywords.slice(0, 10).map(k => k.keyword).join(', ');
  }

  formatKeywordList(keywords) {
    if (!keywords || keywords.length === 0) return 'None available';
    
    return keywords.slice(0, 5).map(k => 
      `${k.keyword} (ROAS: ${k.roas?.toFixed(2) || 'N/A'}, Spend: $${k.spend?.toFixed(2) || '0'})`
    ).join(', ');
  }

  structureInsights(mainInsights, trendInsights, competitiveInsights) {
    return {
      main: mainInsights,
      trends: {
        analysis: trendInsights,
        summary: this.extractTrendSummary(trendInsights)
      },
      competitive: {
        analysis: competitiveInsights,
        opportunities: this.extractCompetitiveOpportunities(competitiveInsights)
      },
      actionable: this.generateActionableInsights(mainInsights),
      confidence: this.calculateConfidenceScore(mainInsights)
    };
  }

  extractTrendSummary(trendText) {
    // Extract key trend points
    const trends = [];
    if (trendText.includes('increasing') || trendText.includes('growing')) {
      trends.push('Growth trend identified');
    }
    if (trendText.includes('declining') || trendText.includes('decreasing')) {
      trends.push('Declining performance detected');
    }
    return trends;
  }

  extractCompetitiveOpportunities(competitiveText) {
    const opportunities = [];
    if (competitiveText.includes('gap') || competitiveText.includes('opportunity')) {
      opportunities.push('Market gap identified');
    }
    if (competitiveText.includes('expansion') || competitiveText.includes('new market')) {
      opportunities.push('Expansion opportunity available');
    }
    return opportunities;
  }

  generateActionableInsights(mainInsights) {
    const actionable = [];
    
    if (mainInsights.immediateOpportunities) {
      mainInsights.immediateOpportunities.forEach(opp => {
        actionable.push({
          action: opp,
          priority: 'high',
          timeframe: 'immediate'
        });
      });
    }

    return actionable;
  }

  calculateConfidenceScore(insights) {
    let score = 70; // Base confidence
    
    if (insights.keyFindings && insights.keyFindings.length >= 3) score += 10;
    if (insights.strategicInsights && insights.strategicInsights.length >= 3) score += 10;
    if (insights.immediateOpportunities && insights.immediateOpportunities.length >= 2) score += 10;
    
    return Math.min(100, score);
  }
}

module.exports = InsightGeneratorAgent;
