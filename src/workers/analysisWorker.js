const { Worker } = require('bullmq');
const { redisClient } = require('../config/redis');
const AnalysisService = require('../services/analysisService');
const logger = require('../config/logger');

const analysisService = new AnalysisService();

// Create analysis worker
const analysisWorker = new Worker('analysis', async (job) => {
  const { jobId, filePath, filename } = job.data;
  
  logger.info(`Starting analysis worker for job ${jobId}`, {
    jobId,
    filename,
    filePath
  });

  try {
    // Update job progress
    await job.updateProgress(5);
    
    // Perform the analysis
    const result = await analysisService.performAnalysis(jobId, filePath);
    
    // Update job progress to complete
    await job.updateProgress(100);
    
    logger.info(`Analysis worker completed for job ${jobId}`);
    
    return {
      success: true,
      jobId,
      resultId: result.id,
      message: 'Analysis completed successfully'
    };
    
  } catch (error) {
    logger.error(`Analysis worker failed for job ${jobId}:`, error);
    throw error;
  }
}, {
  connection: redisClient,
  concurrency: 3, // Process up to 3 jobs concurrently
  removeOnComplete: 50,
  removeOnFail: 100
});

// Worker event handlers
analysisWorker.on('completed', (job, returnvalue) => {
  logger.info(`Analysis job ${job.id} completed:`, returnvalue);
});

analysisWorker.on('failed', (job, err) => {
  logger.error(`Analysis job ${job.id} failed:`, err);
});

analysisWorker.on('progress', (job, progress) => {
  logger.debug(`Analysis job ${job.id} progress: ${progress}%`);
});

analysisWorker.on('error', (err) => {
  logger.error('Analysis worker error:', err);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, closing analysis worker...');
  await analysisWorker.close();
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, closing analysis worker...');
  await analysisWorker.close();
});

module.exports = analysisWorker;
