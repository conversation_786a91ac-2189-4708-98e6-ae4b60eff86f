const { <PERSON>Job, AnalysisResult, OptimizationTask } = require('../models');
const { addOptimizationJob } = require('../config/queue');
const { async<PERSON><PERSON><PERSON>, NotFoundError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

// Generate optimization tasks for an analysis
const generateOptimizations = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  // Find the analysis job and results
  const job = await AnalysisJob.findByPk(jobId, {
    include: [{
      model: AnalysisResult,
      as: 'result'
    }]
  });

  if (!job) {
    throw new NotFoundError('Analysis job not found');
  }

  if (job.status !== 'completed' || !job.result) {
    throw new ValidationError('Analysis must be completed before generating optimizations');
  }

  // Check if optimizations already exist
  const existingTasks = await OptimizationTask.findAll({
    where: { jobId },
    attributes: ['id', 'title', 'status', 'priority']
  });

  if (existingTasks.length > 0) {
    return res.json({
      success: true,
      message: 'Optimization tasks already exist for this analysis',
      data: {
        jobId,
        existingTasks: existingTasks.length,
        tasks: existingTasks
      }
    });
  }

  // Generate optimization tasks based on analysis results
  const optimizationTasks = await createOptimizationTasks(job, job.result);

  // Add to optimization queue for AI enhancement
  await addOptimizationJob({
    jobId,
    taskIds: optimizationTasks.map(task => task.id)
  });

  logger.info(`Generated ${optimizationTasks.length} optimization tasks for job ${jobId}`);

  res.status(201).json({
    success: true,
    message: 'Optimization tasks generated successfully',
    data: {
      jobId,
      tasksGenerated: optimizationTasks.length,
      tasks: optimizationTasks.map(task => ({
        id: task.id,
        title: task.title,
        category: task.category,
        priority: task.priority,
        estimatedImpact: task.estimatedImpact
      }))
    }
  });
});

// Get optimization tasks for a job
const getOptimizationTasks = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;
  const { status, category, priority } = req.query;

  const whereClause = { jobId };
  
  if (status) whereClause.status = status;
  if (category) whereClause.category = category;
  if (priority) whereClause.priority = priority;

  const tasks = await OptimizationTask.findAll({
    where: whereClause,
    order: [
      ['priority', 'DESC'],
      ['created_at', 'ASC']
    ]
  });

  // Group tasks by category
  const tasksByCategory = tasks.reduce((acc, task) => {
    if (!acc[task.category]) {
      acc[task.category] = [];
    }
    acc[task.category].push(task);
    return acc;
  }, {});

  res.json({
    success: true,
    data: {
      jobId,
      totalTasks: tasks.length,
      tasksByCategory,
      summary: {
        pending: tasks.filter(t => t.status === 'pending').length,
        inProgress: tasks.filter(t => t.status === 'in_progress').length,
        completed: tasks.filter(t => t.status === 'completed').length,
        highPriority: tasks.filter(t => t.priority === 'high' || t.priority === 'critical').length
      }
    }
  });
});

// Get specific optimization task
const getOptimizationTask = asyncHandler(async (req, res) => {
  const { taskId } = req.params;

  const task = await OptimizationTask.findByPk(taskId, {
    include: [{
      model: AnalysisJob,
      as: 'job',
      attributes: ['id', 'originalName', 'completedAt']
    }]
  });

  if (!task) {
    throw new NotFoundError('Optimization task not found');
  }

  res.json({
    success: true,
    data: task
  });
});

// Update optimization task status
const updateOptimizationTask = asyncHandler(async (req, res) => {
  const { taskId } = req.params;
  const { status, notes, actionItemIndex, actionCompleted } = req.body;

  const task = await OptimizationTask.findByPk(taskId);
  if (!task) {
    throw new NotFoundError('Optimization task not found');
  }

  // Update status if provided
  if (status) {
    if (!['pending', 'in_progress', 'completed', 'cancelled'].includes(status)) {
      throw new ValidationError('Invalid status value');
    }
    
    if (status === 'in_progress') {
      await task.markAsInProgress();
    } else if (status === 'completed') {
      await task.markAsCompleted(notes);
    } else {
      task.status = status;
      if (notes) task.notes = notes;
      await task.save();
    }
  }

  // Update specific action item if provided
  if (actionItemIndex !== undefined && actionCompleted !== undefined) {
    await task.updateProgress(actionItemIndex, actionCompleted);
  }

  logger.info(`Updated optimization task ${taskId}: status=${status}`);

  res.json({
    success: true,
    message: 'Task updated successfully',
    data: {
      id: task.id,
      status: task.status,
      updatedAt: task.updatedAt
    }
  });
});

// Delete optimization task
const deleteOptimizationTask = asyncHandler(async (req, res) => {
  const { taskId } = req.params;

  const task = await OptimizationTask.findByPk(taskId);
  if (!task) {
    throw new NotFoundError('Optimization task not found');
  }

  await task.destroy();

  logger.info(`Deleted optimization task ${taskId}`);

  res.json({
    success: true,
    message: 'Task deleted successfully'
  });
});

// Get optimization summary for dashboard
const getOptimizationSummary = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  const tasks = await OptimizationTask.findAll({
    where: { jobId },
    attributes: ['id', 'category', 'priority', 'status', 'estimatedImpact']
  });

  const summary = {
    totalTasks: tasks.length,
    byStatus: {
      pending: tasks.filter(t => t.status === 'pending').length,
      inProgress: tasks.filter(t => t.status === 'in_progress').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length
    },
    byPriority: {
      critical: tasks.filter(t => t.priority === 'critical').length,
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length
    },
    byCategory: tasks.reduce((acc, task) => {
      acc[task.category] = (acc[task.category] || 0) + 1;
      return acc;
    }, {}),
    estimatedImpact: {
      high: tasks.filter(t => t.estimatedImpact === 'high').length,
      medium: tasks.filter(t => t.estimatedImpact === 'medium').length,
      low: tasks.filter(t => t.estimatedImpact === 'low').length
    }
  };

  res.json({
    success: true,
    data: summary
  });
});

// Helper function to create optimization tasks based on analysis
async function createOptimizationTasks(job, analysisResult) {
  const tasks = [];

  // High ACOS optimization
  if (analysisResult.overallACOS > 40) {
    tasks.push(await OptimizationTask.create({
      jobId: job.id,
      title: 'Reduce High ACOS Keywords',
      description: `Current overall ACOS is ${analysisResult.overallACOS.toFixed(2)}%. Focus on reducing spend on underperforming keywords.`,
      category: 'keyword_optimization',
      priority: analysisResult.overallACOS > 60 ? 'critical' : 'high',
      estimatedImpact: 'high',
      estimatedEffort: 'medium',
      targetKeywords: analysisResult.bottomPerformingKeywords.slice(0, 10).map(k => k.keyword),
      currentMetrics: { acos: analysisResult.overallACOS },
      targetMetrics: { acos: 35 },
      actionItems: [
        { action: 'Pause keywords with ACOS > 80%', completed: false },
        { action: 'Reduce bids by 20% on keywords with ACOS 50-80%', completed: false },
        { action: 'Add negative keywords for irrelevant terms', completed: false }
      ],
      reasoning: 'High ACOS indicates poor return on ad spend. Reducing spend on underperforming keywords will improve overall profitability.',
      expectedOutcome: 'Reduce overall ACOS by 10-15% while maintaining sales volume'
    }));
  }

  // Low ROAS optimization
  if (analysisResult.overallROAS < 2) {
    tasks.push(await OptimizationTask.create({
      jobId: job.id,
      title: 'Improve Return on Ad Spend',
      description: `Current ROAS is ${analysisResult.overallROAS.toFixed(2)}x. Focus budget on high-performing keywords.`,
      category: 'budget_reallocation',
      priority: 'high',
      estimatedImpact: 'high',
      estimatedEffort: 'medium',
      targetKeywords: analysisResult.topPerformingKeywords.slice(0, 10).map(k => k.keyword),
      currentMetrics: { roas: analysisResult.overallROAS },
      targetMetrics: { roas: 2.5 },
      actionItems: [
        { action: 'Increase bids on top performing keywords', completed: false },
        { action: 'Allocate more budget to high ROAS campaigns', completed: false },
        { action: 'Optimize product listings for better conversion', completed: false }
      ],
      reasoning: 'Low ROAS indicates inefficient ad spend. Focusing on proven performers will improve returns.',
      expectedOutcome: 'Increase ROAS to 2.5x or higher'
    }));
  }

  // Zero sales keywords
  const zeroSalesCount = analysisResult.lowConversionKeywords?.length || 0;
  if (zeroSalesCount > 0) {
    tasks.push(await OptimizationTask.create({
      jobId: job.id,
      title: 'Remove Non-Converting Keywords',
      description: `${zeroSalesCount} keywords are generating clicks but no sales. Clean up to improve efficiency.`,
      category: 'keyword_optimization',
      priority: 'medium',
      estimatedImpact: 'medium',
      estimatedEffort: 'low',
      targetKeywords: analysisResult.lowConversionKeywords.slice(0, 20).map(k => k.keyword),
      actionItems: [
        { action: 'Review search terms for relevance', completed: false },
        { action: 'Pause irrelevant keywords', completed: false },
        { action: 'Add irrelevant terms as negative keywords', completed: false }
      ],
      reasoning: 'Keywords with clicks but no conversions waste budget and reduce overall campaign efficiency.',
      expectedOutcome: 'Reduce wasted spend and improve overall conversion rate'
    }));
  }

  // Bid optimization for top performers
  if (analysisResult.topPerformingKeywords?.length > 0) {
    tasks.push(await OptimizationTask.create({
      jobId: job.id,
      title: 'Scale Top Performing Keywords',
      description: 'Increase investment in proven high-performing keywords to maximize returns.',
      category: 'bid_adjustment',
      priority: 'high',
      estimatedImpact: 'high',
      estimatedEffort: 'low',
      targetKeywords: analysisResult.topPerformingKeywords.slice(0, 5).map(k => k.keyword),
      actionItems: [
        { action: 'Increase bids by 15-25% on top 5 keywords', completed: false },
        { action: 'Monitor impression share and adjust accordingly', completed: false },
        { action: 'Consider separate campaigns for top performers', completed: false }
      ],
      reasoning: 'Top performing keywords have proven ROI. Scaling them will drive more profitable sales.',
      expectedOutcome: 'Increase sales from top keywords by 20-30%'
    }));
  }

  // Campaign structure optimization
  tasks.push(await OptimizationTask.create({
    jobId: job.id,
    title: 'Optimize Campaign Structure',
    description: 'Review and improve campaign organization for better control and performance.',
    category: 'campaign_structure',
    priority: 'medium',
    estimatedImpact: 'medium',
    estimatedEffort: 'high',
    actionItems: [
      { action: 'Group similar keywords into themed ad groups', completed: false },
      { action: 'Separate high and low performing keywords', completed: false },
      { action: 'Create dedicated campaigns for top products', completed: false },
      { action: 'Implement proper match type strategy', completed: false }
    ],
    reasoning: 'Better campaign structure allows for more precise control and optimization.',
    expectedOutcome: 'Improved control and 10-15% better performance'
  }));

  return tasks;
}

module.exports = {
  generateOptimizations,
  getOptimizationTasks,
  getOptimizationTask,
  updateOptimizationTask,
  deleteOptimizationTask,
  getOptimizationSummary
};
