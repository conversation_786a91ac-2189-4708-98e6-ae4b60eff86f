const { AnalysisJob } = require('../models');
const { addAnalysisJob } = require('../config/queue');
const CSVProcessor = require('../services/csvProcessor');
const { asyncHandler, ValidationError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

const csvProcessor = new CSVProcessor();

// Handle file upload and create analysis job
const uploadFile = asyncHandler(async (req, res) => {
  if (!req.file) {
    throw new ValidationError('No file uploaded');
  }

  const { originalname, filename, path: filePath, size, mimetype } = req.file;

  try {
    // Validate CSV structure
    await csvProcessor.validateCSVStructure(filePath);

    // Get sample data for preview
    const sampleData = await csvProcessor.getSampleData(filePath, 3);

    // Create analysis job record
    const analysisJob = await AnalysisJob.create({
      filename,
      originalName: originalname,
      filePath,
      fileSize: size,
      mimeType: mimetype,
      status: 'pending',
      metadata: {
        uploadedAt: new Date(),
        sampleData,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      }
    });

    // Add job to processing queue
    await addAnalysisJob({
      jobId: analysisJob.id,
      filePath,
      filename: originalname
    });

    logger.info(`File uploaded and analysis job created: ${analysisJob.id}`, {
      filename: originalname,
      size,
      jobId: analysisJob.id
    });

    res.status(201).json({
      success: true,
      message: 'File uploaded successfully and analysis started',
      data: {
        jobId: analysisJob.id,
        filename: originalname,
        fileSize: size,
        status: analysisJob.status,
        estimatedProcessingTime: '2-5 minutes',
        sampleData,
        uploadedAt: analysisJob.createdAt
      }
    });

  } catch (error) {
    logger.error('Upload processing error:', error);
    
    // Clean up uploaded file if there's an error
    const fs = require('fs');
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    throw error;
  }
});

// Get upload status and basic info
const getUploadStatus = asyncHandler(async (req, res) => {
  const { jobId } = req.params;

  const job = await AnalysisJob.findByPk(jobId);
  if (!job) {
    throw new ValidationError('Job not found');
  }

  res.json({
    success: true,
    data: {
      jobId: job.id,
      filename: job.originalName,
      status: job.status,
      progress: job.progress,
      totalRows: job.totalRows,
      processedRows: job.processedRows,
      validRows: job.validRows,
      invalidRows: job.invalidRows,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      processingTime: job.processingTime,
      errorMessage: job.errorMessage,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt
    }
  });
});

// Get recent uploads
const getRecentUploads = asyncHandler(async (req, res) => {
  const limit = parseInt(req.query.limit) || 10;
  const offset = parseInt(req.query.offset) || 0;

  const jobs = await AnalysisJob.findAll({
    order: [['createdAt', 'DESC']],
    limit,
    offset,
    attributes: [
      'id',
      'originalName',
      'fileSize',
      'status',
      'progress',
      'totalRows',
      'validRows',
      'invalidRows',
      'startedAt',
      'completedAt',
      'processingTime',
      'createdAt'
    ]
  });

  const total = await AnalysisJob.count();

  res.json({
    success: true,
    data: {
      jobs,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    }
  });
});

// Get upload statistics
const getUploadStats = asyncHandler(async (req, res) => {
  const stats = await AnalysisJob.findAll({
    attributes: [
      [AnalysisJob.sequelize.fn('COUNT', AnalysisJob.sequelize.col('id')), 'totalJobs'],
      [AnalysisJob.sequelize.fn('COUNT', AnalysisJob.sequelize.literal("CASE WHEN status = 'completed' THEN 1 END")), 'completedJobs'],
      [AnalysisJob.sequelize.fn('COUNT', AnalysisJob.sequelize.literal("CASE WHEN status = 'failed' THEN 1 END")), 'failedJobs'],
      [AnalysisJob.sequelize.fn('COUNT', AnalysisJob.sequelize.literal("CASE WHEN status = 'processing' THEN 1 END")), 'processingJobs'],
      [AnalysisJob.sequelize.fn('COUNT', AnalysisJob.sequelize.literal("CASE WHEN status = 'pending' THEN 1 END")), 'pendingJobs'],
      [AnalysisJob.sequelize.fn('SUM', AnalysisJob.sequelize.col('file_size')), 'totalFileSize'],
      [AnalysisJob.sequelize.fn('AVG', AnalysisJob.sequelize.col('processing_time')), 'avgProcessingTime'],
      [AnalysisJob.sequelize.fn('SUM', AnalysisJob.sequelize.col('total_rows')), 'totalRowsProcessed']
    ],
    raw: true
  });

  // Get recent activity (last 24 hours)
  const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
  const recentActivity = await AnalysisJob.count({
    where: {
      createdAt: {
        [AnalysisJob.sequelize.Op.gte]: yesterday
      }
    }
  });

  res.json({
    success: true,
    data: {
      ...stats[0],
      recentActivity,
      avgProcessingTimeFormatted: stats[0].avgProcessingTime 
        ? `${Math.round(stats[0].avgProcessingTime / 1000)}s` 
        : 'N/A',
      totalFileSizeFormatted: stats[0].totalFileSize 
        ? `${(stats[0].totalFileSize / (1024 * 1024)).toFixed(2)} MB` 
        : '0 MB'
    }
  });
});

// Delete upload job and associated data
const deleteUpload = asyncHandler(async (req, res) => {
  const { jobId } = req.params;

  const job = await AnalysisJob.findByPk(jobId);
  if (!job) {
    throw new ValidationError('Job not found');
  }

  // Clean up file
  const fs = require('fs');
  if (fs.existsSync(job.filePath)) {
    fs.unlinkSync(job.filePath);
  }

  // Delete job (cascade will delete related records)
  await job.destroy();

  logger.info(`Deleted analysis job: ${jobId}`);

  res.json({
    success: true,
    message: 'Upload job deleted successfully'
  });
});

// Retry failed job
const retryJob = asyncHandler(async (req, res) => {
  const { jobId } = req.params;

  const job = await AnalysisJob.findByPk(jobId);
  if (!job) {
    throw new ValidationError('Job not found');
  }

  if (job.status !== 'failed') {
    throw new ValidationError('Only failed jobs can be retried');
  }

  // Reset job status
  job.status = 'pending';
  job.progress = 0;
  job.errorMessage = null;
  job.startedAt = null;
  job.completedAt = null;
  job.processingTime = null;
  await job.save();

  // Add job back to queue
  await addAnalysisJob({
    jobId: job.id,
    filePath: job.filePath,
    filename: job.originalName
  });

  logger.info(`Retrying analysis job: ${jobId}`);

  res.json({
    success: true,
    message: 'Job queued for retry',
    data: {
      jobId: job.id,
      status: job.status
    }
  });
});

module.exports = {
  uploadFile,
  getUploadStatus,
  getRecentUploads,
  getUploadStats,
  deleteUpload,
  retryJob
};
