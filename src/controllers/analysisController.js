const { AnalysisJob, AnalysisResult } = require('../models');
const { cache } = require('../config/redis');
const { asyncHand<PERSON>, NotFoundError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../config/logger');

// Get analysis results by job ID
const getAnalysisResults = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;
  const includeRawData = req.query.includeRawData === 'true';

  // Check cache first
  const cacheKey = `analysis:${jobId}:${includeRawData}`;
  const cachedResult = await cache.get(cacheKey);
  
  if (cachedResult) {
    logger.info(`Serving cached analysis results for job ${jobId}`);
    return res.json({
      success: true,
      data: cachedResult,
      cached: true
    });
  }

  // Find job with results
  const job = await AnalysisJob.findByPk(jobId, {
    include: [{
      model: AnalysisResult,
      as: 'result'
    }]
  });

  if (!job) {
    throw new NotFoundError('Analysis job not found');
  }

  // Check if analysis is complete
  if (job.status === 'pending' || job.status === 'processing') {
    return res.json({
      success: true,
      data: {
        jobId: job.id,
        status: job.status,
        progress: job.progress,
        message: job.status === 'pending' 
          ? 'Analysis is queued for processing' 
          : 'Analysis is currently in progress',
        estimatedTimeRemaining: job.status === 'processing' 
          ? Math.max(0, 300 - (Date.now() - new Date(job.startedAt).getTime()) / 1000)
          : null
      }
    });
  }

  if (job.status === 'failed') {
    return res.json({
      success: false,
      data: {
        jobId: job.id,
        status: job.status,
        error: job.errorMessage,
        message: 'Analysis failed. Please try uploading the file again.'
      }
    });
  }

  if (!job.result) {
    throw new NotFoundError('Analysis results not found');
  }

  // Prepare response data
  const responseData = {
    job: {
      id: job.id,
      filename: job.originalName,
      status: job.status,
      progress: job.progress,
      totalRows: job.totalRows,
      validRows: job.validRows,
      invalidRows: job.invalidRows,
      processingTime: job.processingTime,
      completedAt: job.completedAt,
      createdAt: job.createdAt
    },
    results: {
      overall: {
        totalImpressions: job.result.totalImpressions,
        totalClicks: job.result.totalClicks,
        totalSpend: parseFloat(job.result.totalSpend),
        totalSales: parseFloat(job.result.totalSales),
        totalOrders: job.result.totalOrders,
        overallCTR: parseFloat(job.result.overallCTR),
        overallCPC: parseFloat(job.result.overallCPC),
        overallACOS: parseFloat(job.result.overallACOS),
        overallROAS: parseFloat(job.result.overallROAS),
        overallConversionRate: parseFloat(job.result.overallConversionRate)
      },
      performance: {
        topPerformingKeywords: job.result.topPerformingKeywords,
        bottomPerformingKeywords: job.result.bottomPerformingKeywords,
        highSpendKeywords: job.result.highSpendKeywords,
        lowConversionKeywords: job.result.lowConversionKeywords
      },
      insights: {
        keyFindings: job.result.keyFindings,
        recommendations: job.result.recommendations
      },
      distributions: {
        keyword: job.result.keywordDistribution,
        spend: job.result.spendDistribution
      },
      trends: job.result.performanceTrends
    }
  };

  // Cache the results for 1 hour
  await cache.set(cacheKey, responseData, 3600);

  logger.info(`Served analysis results for job ${jobId}`);

  res.json({
    success: true,
    data: responseData,
    cached: false
  });
});

// Get analysis summary (lightweight version)
const getAnalysisSummary = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  const job = await AnalysisJob.findByPk(jobId, {
    include: [{
      model: AnalysisResult,
      as: 'result',
      attributes: [
        'totalSpend',
        'totalSales',
        'overallROAS',
        'overallACOS',
        'overallConversionRate'
      ]
    }],
    attributes: [
      'id',
      'originalName',
      'status',
      'progress',
      'totalRows',
      'validRows',
      'completedAt'
    ]
  });

  if (!job) {
    throw new NotFoundError('Analysis job not found');
  }

  const summary = {
    jobId: job.id,
    filename: job.originalName,
    status: job.status,
    progress: job.progress,
    totalRows: job.totalRows,
    validRows: job.validRows,
    completedAt: job.completedAt
  };

  if (job.result) {
    summary.metrics = {
      totalSpend: parseFloat(job.result.totalSpend),
      totalSales: parseFloat(job.result.totalSales),
      roas: parseFloat(job.result.overallROAS),
      acos: parseFloat(job.result.overallACOS),
      conversionRate: parseFloat(job.result.overallConversionRate)
    };
  }

  res.json({
    success: true,
    data: summary
  });
});

// Compare multiple analyses
const compareAnalyses = asyncHandler(async (req, res) => {
  const { jobIds } = req.body;

  if (!Array.isArray(jobIds) || jobIds.length < 2 || jobIds.length > 5) {
    throw new ValidationError('Please provide 2-5 job IDs for comparison');
  }

  const jobs = await AnalysisJob.findAll({
    where: {
      id: jobIds,
      status: 'completed'
    },
    include: [{
      model: AnalysisResult,
      as: 'result'
    }],
    attributes: ['id', 'originalName', 'completedAt', 'totalRows']
  });

  if (jobs.length !== jobIds.length) {
    throw new ValidationError('Some jobs not found or not completed');
  }

  const comparison = {
    jobs: jobs.map(job => ({
      id: job.id,
      filename: job.originalName,
      completedAt: job.completedAt,
      totalRows: job.totalRows
    })),
    metrics: {
      spend: jobs.map(job => ({
        jobId: job.id,
        value: parseFloat(job.result.totalSpend)
      })),
      sales: jobs.map(job => ({
        jobId: job.id,
        value: parseFloat(job.result.totalSales)
      })),
      roas: jobs.map(job => ({
        jobId: job.id,
        value: parseFloat(job.result.overallROAS)
      })),
      acos: jobs.map(job => ({
        jobId: job.id,
        value: parseFloat(job.result.overallACOS)
      })),
      conversionRate: jobs.map(job => ({
        jobId: job.id,
        value: parseFloat(job.result.overallConversionRate)
      }))
    }
  };

  res.json({
    success: true,
    data: comparison
  });
});

// Get analysis insights only
const getAnalysisInsights = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;

  const job = await AnalysisJob.findByPk(jobId, {
    include: [{
      model: AnalysisResult,
      as: 'result',
      attributes: ['keyFindings', 'recommendations', 'aiInsights']
    }]
  });

  if (!job) {
    throw new NotFoundError('Analysis job not found');
  }

  if (job.status !== 'completed' || !job.result) {
    throw new ValidationError('Analysis not completed or results not available');
  }

  res.json({
    success: true,
    data: {
      jobId: job.id,
      insights: {
        keyFindings: job.result.keyFindings,
        recommendations: job.result.recommendations,
        aiInsights: job.result.aiInsights
      }
    }
  });
});

// Export analysis data
const exportAnalysis = asyncHandler(async (req, res) => {
  const { id: jobId } = req.params;
  const { format = 'json' } = req.query;

  const job = await AnalysisJob.findByPk(jobId, {
    include: [{
      model: AnalysisResult,
      as: 'result'
    }]
  });

  if (!job) {
    throw new NotFoundError('Analysis job not found');
  }

  if (job.status !== 'completed' || !job.result) {
    throw new ValidationError('Analysis not completed');
  }

  const exportData = {
    exportedAt: new Date().toISOString(),
    job: {
      id: job.id,
      filename: job.originalName,
      completedAt: job.completedAt,
      processingTime: job.processingTime
    },
    results: job.result
  };

  if (format === 'csv') {
    // Convert to CSV format
    const csv = convertToCSV(exportData);
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="analysis-${jobId}.csv"`);
    return res.send(csv);
  }

  // Default JSON format
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Disposition', `attachment; filename="analysis-${jobId}.json"`);
  res.json(exportData);
});

// Helper function to convert data to CSV
function convertToCSV(data) {
  const rows = [];
  
  // Add overall metrics
  rows.push(['Metric', 'Value']);
  rows.push(['Total Spend', data.results.totalSpend]);
  rows.push(['Total Sales', data.results.totalSales]);
  rows.push(['Overall ROAS', data.results.overallROAS]);
  rows.push(['Overall ACOS', data.results.overallACOS]);
  rows.push(['Conversion Rate', data.results.overallConversionRate]);
  
  // Add top performers
  rows.push(['']);
  rows.push(['Top Performing Keywords']);
  rows.push(['Keyword', 'ROAS', 'Sales', 'Spend']);
  
  data.results.topPerformingKeywords.forEach(keyword => {
    rows.push([keyword.keyword, keyword.roas, keyword.sales, keyword.spend]);
  });

  return rows.map(row => row.join(',')).join('\n');
}

module.exports = {
  getAnalysisResults,
  getAnalysisSummary,
  compareAnalyses,
  getAnalysisInsights,
  exportAnalysis
};
