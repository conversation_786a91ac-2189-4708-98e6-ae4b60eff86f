const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { ValidationError } = require('./errorHandler');
const logger = require('../config/logger');

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_DIR || './uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp and UUID
    const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
    const extension = path.extname(file.originalname);
    const filename = `${file.fieldname}-${uniqueSuffix}${extension}`;
    cb(null, filename);
  },
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedMimeTypes = [
    'text/csv',
    'application/csv',
    'text/plain',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];

  const allowedExtensions = ['.csv', '.txt', '.xls', '.xlsx'];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  if (allowedMimeTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
    cb(null, true);
  } else {
    cb(new ValidationError('Invalid file type. Only CSV, TXT, XLS, and XLSX files are allowed.'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 100 * 1024 * 1024, // 100MB default
    files: 1, // Only allow one file at a time
  },
});

// Middleware to handle single file upload
const uploadSingle = (fieldName = 'file') => {
  return (req, res, next) => {
    const uploadMiddleware = upload.single(fieldName);
    
    uploadMiddleware(req, res, (err) => {
      if (err) {
        logger.error('File upload error:', err);
        
        if (err instanceof multer.MulterError) {
          switch (err.code) {
            case 'LIMIT_FILE_SIZE':
              return next(new ValidationError('File size too large. Maximum size is 100MB.'));
            case 'LIMIT_FILE_COUNT':
              return next(new ValidationError('Too many files. Only one file is allowed.'));
            case 'LIMIT_UNEXPECTED_FILE':
              return next(new ValidationError(`Unexpected file field. Expected field name: ${fieldName}`));
            default:
              return next(new ValidationError(`Upload error: ${err.message}`));
          }
        }
        
        return next(err);
      }
      
      // Check if file was uploaded
      if (!req.file) {
        return next(new ValidationError('No file uploaded. Please select a file to upload.'));
      }
      
      // Add file metadata to request
      req.fileMetadata = {
        originalName: req.file.originalname,
        filename: req.file.filename,
        path: req.file.path,
        size: req.file.size,
        mimetype: req.file.mimetype,
        uploadedAt: new Date(),
      };
      
      logger.info('File uploaded successfully:', {
        originalName: req.file.originalname,
        filename: req.file.filename,
        size: req.file.size,
        mimetype: req.file.mimetype,
      });
      
      next();
    });
  };
};

// Middleware to validate uploaded file
const validateUploadedFile = (req, res, next) => {
  if (!req.file) {
    return next(new ValidationError('No file uploaded'));
  }

  const file = req.file;
  
  // Additional validation
  if (file.size === 0) {
    return next(new ValidationError('Uploaded file is empty'));
  }

  // Check if file exists on disk
  if (!fs.existsSync(file.path)) {
    return next(new ValidationError('Uploaded file not found on server'));
  }

  // For CSV files, do a quick validation of the first few lines
  if (file.mimetype === 'text/csv' || path.extname(file.originalname).toLowerCase() === '.csv') {
    try {
      const fileContent = fs.readFileSync(file.path, 'utf8');
      const lines = fileContent.split('\n').slice(0, 5); // Check first 5 lines
      
      if (lines.length < 2) {
        return next(new ValidationError('CSV file must contain at least a header and one data row'));
      }
      
      // Check if first line looks like a header
      const header = lines[0];
      if (!header || header.trim().length === 0) {
        return next(new ValidationError('CSV file must contain a valid header row'));
      }
      
    } catch (error) {
      logger.error('Error validating CSV file:', error);
      return next(new ValidationError('Error reading uploaded file'));
    }
  }

  next();
};

// Cleanup function to remove uploaded files
const cleanupFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`Cleaned up file: ${filePath}`);
    }
  } catch (error) {
    logger.error(`Error cleaning up file ${filePath}:`, error);
  }
};

// Middleware to cleanup file on error
const cleanupOnError = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // If there's an error and a file was uploaded, clean it up
    if (res.statusCode >= 400 && req.file) {
      cleanupFile(req.file.path);
    }
    originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  uploadSingle,
  validateUploadedFile,
  cleanupFile,
  cleanupOnError,
};
