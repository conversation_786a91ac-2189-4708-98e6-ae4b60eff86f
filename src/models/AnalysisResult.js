const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AnalysisResult = sequelize.define('AnalysisResult', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  jobId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'analysis_jobs',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  // Overall metrics
  totalImpressions: {
    type: DataTypes.BIGINT,
    defaultValue: 0,
  },
  totalClicks: {
    type: DataTypes.BIGINT,
    defaultValue: 0,
  },
  totalSpend: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
  },
  totalSales: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
  },
  totalOrders: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  // Calculated metrics
  overallCTR: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
  },
  overallCPC: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
  },
  overallACOS: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
  },
  overallROAS: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
  },
  overallConversionRate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
  },
  // Performance insights
  topPerformingKeywords: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  bottomPerformingKeywords: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  highSpendKeywords: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  lowConversionKeywords: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  // Statistical analysis
  performanceTrends: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
  keywordDistribution: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
  spendDistribution: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
  // AI-generated insights
  aiInsights: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  keyFindings: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  recommendations: {
    type: DataTypes.JSON,
    defaultValue: [],
  },
  // Metadata
  analysisVersion: {
    type: DataTypes.STRING,
    defaultValue: '1.0',
  },
  processingTime: {
    type: DataTypes.INTEGER, // in milliseconds
    allowNull: true,
  },
}, {
  tableName: 'analysis_results',
  indexes: [
    {
      fields: ['job_id'],
      unique: true,
    },
    {
      fields: ['created_at'],
    },
    {
      fields: ['overall_r_o_a_s'],
    },
    {
      fields: ['overall_a_c_o_s'],
    },
  ],
});

// Instance methods
AnalysisResult.prototype.calculateOverallMetrics = function(data) {
  // Calculate overall metrics from processed data
  const totals = data.reduce((acc, row) => {
    acc.impressions += parseInt(row.impressions) || 0;
    acc.clicks += parseInt(row.clicks) || 0;
    acc.spend += parseFloat(row.spend) || 0;
    acc.sales += parseFloat(row.sales) || 0;
    acc.orders += parseInt(row.orders) || 0;
    return acc;
  }, { impressions: 0, clicks: 0, spend: 0, sales: 0, orders: 0 });

  this.totalImpressions = totals.impressions;
  this.totalClicks = totals.clicks;
  this.totalSpend = totals.spend;
  this.totalSales = totals.sales;
  this.totalOrders = totals.orders;

  // Calculate derived metrics
  this.overallCTR = totals.impressions > 0 ? (totals.clicks / totals.impressions * 100) : 0;
  this.overallCPC = totals.clicks > 0 ? (totals.spend / totals.clicks) : 0;
  this.overallACOS = totals.sales > 0 ? (totals.spend / totals.sales * 100) : 0;
  this.overallROAS = totals.spend > 0 ? (totals.sales / totals.spend) : 0;
  this.overallConversionRate = totals.clicks > 0 ? (totals.orders / totals.clicks * 100) : 0;
};

AnalysisResult.prototype.identifyTopPerformers = function(data, limit = 10) {
  // Sort by ROAS descending
  const sortedByROAS = data
    .filter(row => parseFloat(row.roas) > 0)
    .sort((a, b) => parseFloat(b.roas) - parseFloat(a.roas))
    .slice(0, limit)
    .map(row => ({
      keyword: row.matchedProduct,
      roas: parseFloat(row.roas),
      sales: parseFloat(row.sales),
      spend: parseFloat(row.spend),
      orders: parseInt(row.orders)
    }));

  this.topPerformingKeywords = sortedByROAS;
};

AnalysisResult.prototype.identifyBottomPerformers = function(data, limit = 10) {
  // Sort by ACOS descending (worst performers)
  const sortedByACOS = data
    .filter(row => parseFloat(row.acos) > 0)
    .sort((a, b) => parseFloat(b.acos) - parseFloat(a.acos))
    .slice(0, limit)
    .map(row => ({
      keyword: row.matchedProduct,
      acos: parseFloat(row.acos),
      sales: parseFloat(row.sales),
      spend: parseFloat(row.spend),
      orders: parseInt(row.orders)
    }));

  this.bottomPerformingKeywords = sortedByACOS;
};

module.exports = AnalysisResult;
