#!/usr/bin/env node

/**
 * Worker Process Starter
 * Starts the background workers for processing analysis and optimization jobs
 */

require('dotenv').config();
const logger = require('./src/config/logger');

// Import workers
const analysisWorker = require('./src/workers/analysisWorker');
const optimizationWorker = require('./src/workers/optimizationWorker');

// Start workers
logger.info('Starting AakaarAI workers...');

// Handle process termination
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down workers gracefully...');
  
  try {
    await Promise.all([
      analysisWorker.close(),
      optimizationWorker.close()
    ]);
    logger.info('All workers closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error closing workers:', error);
    process.exit(1);
  }
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down workers gracefully...');
  
  try {
    await Promise.all([
      analysisWorker.close(),
      optimizationWorker.close()
    ]);
    logger.info('All workers closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error closing workers:', error);
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception in workers:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection in workers at:', promise, 'reason:', reason);
  process.exit(1);
});

logger.info('Workers started successfully');
logger.info('Analysis worker: Processing CSV analysis jobs');
logger.info('Optimization worker: Processing AI optimization jobs');
logger.info('Press Ctrl+C to stop workers');

// Keep the process alive
setInterval(() => {
  // Health check or periodic maintenance can go here
}, 30000); // Every 30 seconds
